{"name": "rapsap", "version": "1.5.24", "description": "", "scripts": {"start": "./node_modules/pm2/bin/pm2-runtime api/index.js", "dev": "nodemon api/index.js", "poststart": "node ./node_modules/pm2/bin/pm2 logs", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "dependencies": {"aws-sdk": "^2.917.0", "axios": "^0.21.1", "bcrypt": "^5.0.1", "body-parser": "^1.19.0", "cors": "^2.8.5", "csvtojson": "^2.0.10", "dotenv": "^10.0.0", "express": "^4.17.1", "firebase-admin": "^10.0.0", "jsonwebtoken": "^8.5.1", "lodash": "^4.17.21", "multer": "^1.4.2", "multiparty": "^4.2.2", "mysql2": "^2.2.5", "node-fetch": "^2.6.1", "pm2": "^5.3.0", "razorpay": "^2.0.6", "update": "^0.7.4", "uuid": "^8.3.2"}}