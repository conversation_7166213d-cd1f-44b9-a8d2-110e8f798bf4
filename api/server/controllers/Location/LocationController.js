
const db = require('../../../config/connection');

const { getServiceable } = require('../../services/LocationService/LocationService');
const { checklatlongService } = require('../../services/LocationService/DeliveryService');
const { requiredParams } = require('../../utility/requiredCheck');
const { default: axios } = require('axios');

class LocationController {
  static async serviceable(req, res) {
    const payload = req.body;
    const { pincode } = payload;
    if (pincode == null || pincode == undefined) return requiredParams(res, 'pincode is required!');
    try {
      const [[result]] = await getServiceable(payload);
      console.log('result', result);
      if (result) {
        return res.status(200).send({
          success: true,
          msg: 'Service available',
          data: result
        });
      } else {
        return res.status(200).send({
          success: false,
          msg: 'service unavailable.',
          data: {}
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {}
      });
    }
  }

  static async checklatlongRadius(req, res) {
    const payload = req.body;
    const { pincode, lat, long } = payload;
    if (pincode == null || pincode == undefined) return requiredParams(res, 'pincode is required!');
    if (lat == null || lat == undefined) return requiredParams(res, 'lat is required!');
    if (long == null || long == undefined) return requiredParams(res, 'long is required!');

    try {
      const { row, pin } = await checklatlongService(payload);
      // console.log('row1', row);
      // console.log('pin1', pin);
      if (row.isFast == 0) {
        if (pin == null || pin == undefined) {
          return res.status(200).send({
            success: false,
            msg: 'service unavailable.',
            data: {}
          });
        } else {
          return res.status(200).send({
            success: true,
            msg: 'Service available for Lastmile (Pincode) Delivery',
            data: {
              distance_in_km: 0,
              store_code: pin.store_code,
              store_id: pin.store_id,
              store_name: pin.store_name,
              store_contact: pin.contact_no,
              radius: undefined,
              isFast: 0
            }
          });
        }
      } else {
        if (row) {
          return res.status(200).send({
            success: true,
            msg: 'Service available for Hyper Local delivery.',
            data: row
          });
        } else {
          return res.status(200).send({
            success: false,
            msg: 'service unavailable.',
            data: {}
          });
        }
      }

    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {}
      });
    }
  }

  static async deliveryFeeCheck(req, res) {
    const payload = req.body;
    const { store_code, order_id, drop_latitude, drop_longitude } = payload;
    if (store_code == null || store_code == undefined) return requiredParams(res, 'store_code is required!');
    if (order_id == null || order_id == undefined) return requiredParams(res, 'order_id is required!');
    if (drop_latitude == null || drop_latitude == undefined) return requiredParams(res, 'drop_latitude is required!');
    if (drop_longitude == null || drop_longitude == undefined) return requiredParams(res, 'drop_longitude is required!');


    const [[clientIntegration]] = await db.promise().query(`select * from client_integration where type = 'delivery' and is_active =1`);

    const reqPayload = {
      ...payload,
      "stage_of_check": "pre_order"
    }
    const options = {
      'method': 'PUT',
      'url': `${clientIntegration.domain}/api/v2/store_serviceability/`,
      'headers': {
        'Authorization': clientIntegration.secret,
        'Content-Type': 'application/json'
      },
      data: JSON.stringify(reqPayload)
    };
    try {
      const deliveryResult = await axios(options);
      if (deliveryResult) {
        return res.status(200).send({
          success: true,
          msg: 'Service available',
          data: deliveryResult.data
        });
      } else {
        return res.status(200).send({
          success: false,
          msg: 'service unavailable.',
          data: {}
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {}
      });
    }
  }
}

module.exports = LocationController;