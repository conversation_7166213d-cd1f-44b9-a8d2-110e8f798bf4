const { totalUsersService, OrdersChartService, dashboardCountsService, getStoresService } = require("../../services/DashboardServices/DashboardService");
const { getgofrugalkeys } = require("../../services/EcommServices/GofrugalService");
const axios=require("axios");
const { convertDateFormat } = require("../../utility/dateformat");


class DashboardController {
    static async totalUsers(req, res) {
        const payload = req.body;
        const result = await totalUsersService(payload);
        try {
            if (result) {
                return res.status(200).send({
                    success: true,
                    msg: ' Total Users',
                    data: result
                });
            }
        } catch (error) {
            return res.status(200).send({
                success: false,
                msg: error.message || `Something went wrong`,
                data: []
            });
        }
    }


    static async dashboardCounts(req, res) {
        const payload = req.body;
        const result = await dashboardCountsService(payload);
        try {
            if (result) {
                return res.status(200).send({
                    success: true,
                    msg: ' Total counts',
                    data: {
                        total_users:result.users.total_users,
                        total_orders: result.orders.total_orders,
                        total_products: result.products.total_products, 
                        total_revenue: result.revenue.total_revenue == null ? 0 : result.revenue.total_revenue,
                        topProduct:result.topProduct[0]
                    }
                });
            }
        } catch (error) {
            return res.status(200).send({
                success: false,
                msg: error.message || `Something went wrong`,
                data: []
            });
        }
    }


    static async OrdersChart(req, res) {
        const payload = req.body;
        const result = await OrdersChartService(payload);
        try {
            if (result) {
                return res.status(200).send({
                    success: true,
                    msg: 'Orders status count',
                    data: result,
                });
            }
        } catch (error) {
            return res.status(200).send({
                success: false,
                msg: error.message || `Something went wrong`,
                data: []
            });
        }
    }

    static async getStores(req, res) {
        const payload = req.body;
        try {
          const [result,meta] = await getStoresService(payload);
          if (result) {
            return res.status(200).send({
              success: true,
              msg: (result.length !== 0) ? 'Store list' : 'No Stores',
              data: result,
              meta: meta[0] || { total: 0 }
            });
          }
        } catch (error) {
          return res.status(200).send({
            success: false,
            msg: error.message || `Something went wrong`,
            data: []
          });
        }
      }


      static async getGofrugalPurchase(req, res) {
        const payload = req.body;
        try {
          const [[{ domainpath, token }]] = await getgofrugalkeys();
          const { start_date, end_date, page, size, sort } = payload;
          let customFilter = "";
          if (start_date && end_date) {
            customFilter = `?q=PoDate>=${convertDateFormat(start_date, "start")},PoDate<=${convertDateFormat(end_date, "end")}`;
          }
          if (page) {
            customFilter += customFilter ? `&page=${page + 1}` : `?page=${page + 1}`;
          }
          if (size) {
            customFilter += customFilter ? `&limit=${size}` : `?limit=${size}`;
          }
          if (sort?.field === "createdAt" && sort?.order_by === "desc") {
            customFilter += customFilter ? `&order=${sort?.field}` : `?order==${sort?.field}`;
          }
          const options = {
            'method': 'GET',
            'url': `${domainpath}/api/v1/purchaseOrders${customFilter}`,
            'headers': {
              'Content-Type': 'application/json',
              'X-Auth-Token': token
            },
          };
          const result = await axios(options);
          if (result) {
            return res.status(200).send({
              success: true,
              msg: !result.data.purchaseOrders ? "No purchase found" : "Gofrugal Purchase",
              data: !result.data.purchaseOrders ? [] : result.data.purchaseOrders,
              meta: {
                total: result.data.total_records
              }
            });
          }
        } catch (error) {
          return res.status(200).send({
            success: false,
            msg: error.message || `Something went wrong`,
            data: []
          });
        }
      }
      

}

module.exports = DashboardController;