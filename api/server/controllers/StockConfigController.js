const { updateConfigService,getConfigService } = require("../services/StockConfigService");
const { requiredParams } = require('../utility/requiredCheck');

class StockConfigController {
  static async updateConfig(req, res) {
    const payload = req.body;
    //console.log('createConfig payload', payload);
    const { out_of_stock, finishing_soon, in_stock, plenty } = payload;
    if (out_of_stock == null || out_of_stock == undefined) return requiredParams(res, 'out_of_stock is required!');
    if (finishing_soon == null || finishing_soon == undefined) return requiredParams(res, 'finishing_soon is required!');
    if (plenty == null || plenty == undefined) return requiredParams(res, 'plenty is required!');
    if (in_stock == null || in_stock == undefined) return requiredParams(res, 'in_stock is required!');
    const [result] = await updateConfigService(payload);
    //console.log('result', result);
    try {
      if (result) {
        return res.status(200).send({
          success: true,
          msg: `${result.msg}`,
          data: {},
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: []
      });
    }
  }

  static async getConfig(req, res) {
    const payload = req.body;
    //console.log('getConfig payload', payload);
    const [result] = await getConfigService(payload);
    // console.log('result', result);
    try {
      if (result) {
        return res.status(200).send({
          success: true,
          msg: `Config Details! `,
          data: result,
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: []
      });
    }
  }  
}

module.exports = StockConfigController;