
const { getUserDetailService,verifyOtpService, getOfferByUserIDService, getUserRewardsService, openOfferByUserService, claimOfferByUserService, createAddressService, getAddressService, updateAddressService, deleteAddressService, getUsersListService, deleteUserImageService, saveDeviceTokenService, logoutService, deactivateUserService, editProfileService, createTempidService, sendOtpService,requestAccountDeleteService, userConfigService, updateUserConfigService, saveDetailsForNotificationService, loginWithGoogleIdTokenService } = require("../../services/UserService/UserService");
const { requiredParams } = require("../../utility/requiredCheck");
const { getWhatsappService, getnotificationService, getDynamicLink } = require("../../services/NotificationServices/NotificationServices");
const { getAuth } = require('firebase-admin/auth');


const db = require('../../../config/connection');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');

const aws = require('aws-sdk');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const { uploadImageService } = require("../../services/EcommServices/ProductService");
aws.config.setPromisesDependency();
aws.config.update({
  accessKeyId: process.env.accessKeyId,
  secretAccessKey: process.env.secretAccessKey,
  region: process.env.region
});
const s3 = new aws.S3();

class UserController {
  static async registerUser(req, res) {
    const password = req.body.password;
    const mobile = req.body.mobile;
    const encryptedPassword = await bcrypt.hash(password, 10)
    const users = {
      "email": req.body.email,
      "mobile": req.body.mobile,
      "password": encryptedPassword,
      "first_name": req.body.name
    }
    const checkSQL = `select count(*) as status from users where mobile = ? or email = ?`;
    const [[isExist]] = await db.promise().query(checkSQL, [req.body.mobile, req.body.email]);
    console.log('isExist user', isExist);
    if (isExist.status > 0) return requiredParams(res, 'User already exist!');
    db.query('INSERT INTO users SET ?', users, function (error, results, fields) {
      console.log(' register results', results);
        if (error) {
          return res.status(200).send({
            success: false,
            msg: error.message || `Something went wrong`,
          data: {}
          });
        } else {
          const token = jwt.sign({ result: mobile }, process.env.JWT_KEY, {
          expiresIn: '30d',
          });
          let data = {};
        db.query('select u.id as user_id ,u.first_name,u.last_name,u.email,u.mobile,u.is_active,u.role_id,u.create_at as created_at from users u where u.id =  ?', results.insertId, function (error, user) {
              data = user[0];
              data.token = token;
              return res.status(200).send({
                success: true,
            msg: 'User created successfully.',
            data: data
          }); 
              });
            }
    });
  }

  static async loginUser(req, res) {
    const username = req.body.username;
    const password = req.body.password;
    const sql = `SELECT * FROM users WHERE email = ${JSON.stringify(username)} or mobile = ${JSON.stringify(username)}` ;
    // : `SELECT * FROM users WHERE email = ?`
    db.query(sql, async function (error, results, fields) {
      if (error) {
        return res.status(200).send({
          success: false,
          msg: error.message || `Something went wrong`,
          data: {}
        });
      } else {
        if (results.length > 0) {
          const comparision = await bcrypt.compare(password, results[0].password);
          console.log('results', results[0]);
          if (comparision) {
            if(results[0].email == null || (results[0].email).trim() == "" ){
              await getnotificationService({
                receiver_id : results[0].id, 
                data:{title : "Add email address",body :"Add your email address to rapsap",type : "profile" }
              });
            }
            results[0].password = undefined;
            const token = jwt.sign({ result: results[0].mobile }, process.env.JWT_KEY, {
              expiresIn: '30d',
            });
            let data = {};
            data = results[0];
            data.token = token;
            return res.status(200).send({
              success: true,
              msg: 'Login successfully!',
              data: data
            });
          }
          else {
            return res.status(200).send({
              success: false,
              msg: 'username and password does not match.',
              data: {}
            });
          }
        }
        else {
          return res.status(200).send({
            success: false,
            msg: 'User does not exist!',
            data: {}
          });
        }
      }
    });
  }

  static async loginWithMobile(req, res) {
    const mobile = req.body.mobile;
    if (mobile == null || mobile == undefined) return requiredParams(res, 'mobile is required!');
    db.query('SELECT * FROM users WHERE mobile = ? limit 1', [mobile], async function (error, results, fields) {
        if (error) {
          return res.status(200).send({
            success: false,
            msg: error.message || `Something went wrong`,
          data: {}
          });
        } else {
          if (results.length > 0) {
          const token = jwt.sign({ result: results[0].mobile }, process.env.JWT_KEY, {
            expiresIn: '30d',
          });
            results[0].password = undefined;
            let data = {};
            data = results[0];
            data.token = token;

            return res.status(200).send({
              success: true,
            msg: 'Login successfully!',
              data: data,
            });
        }
        else {
            return res.status(200).send({
              success: false,
            msg: 'User does not exist!',
            data: {}
            });
          }
        }
    });
  }

  static async loginWithGoogleIdToken(req, res) {
    const {id_token} = req.body;
    if (!id_token) return requiredParams(res, 'id_token is required!');
    let tokenData;
    try {
      tokenData = await getAuth().verifyIdToken(id_token)
    } catch (error) {
      return res.status(401).send({
        success: false,
        msg: error.message || "Couldn't verify id_token",
        data: {},
      });
    }
    try {
      const result = await loginWithGoogleIdTokenService(tokenData);
      const token = jwt.sign(
        { result: result.email }, 
        process.env.JWT_KEY, 
        { expiresIn: '30d'}
      );
      result.password = undefined;
      result.sent_otp = undefined;
      result.password_reset_token = undefined;
      result.token = token;
      if (result?.email) {
        return res.status(200).send({
          success: true,
          msg: "Logged in successfully",
          data: result,
        });
      } else {
        return res.status(401).send({
          success: false,
          msg:"Couldn't get user data",
          data: {},
        });
      }
    } catch (error) {
      return res.status(500).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {}
      });
    }
  }

  static async sendOtp(req, res) {
    const payload = req.body;
    const { mobile, name } = payload;
    const otp = Math.random().toFixed(6).substr(-6);
    console.log("otp", otp);
    const encryptedOtp = await bcrypt.hash(otp, 10)
    const userPayload = { ...payload, otp: encryptedOtp, type: "temp" };
    if (mobile == null || mobile == undefined) return requiredParams(res, 'mobile is required!',406);
    if (String(mobile).length !== 10 ) return requiredParams(res, 'Invalid mobile number',406);
    const checkSQL = `select count(*) as status, id as temp_id,user_id,first_name from temp_user where mobile = ?`;
    const [[isExist]] = await db.promise().query(checkSQL, mobile);
    console.log('isExist user', isExist);
    let result;
    try {
      const {shortLink}= await getDynamicLink(`${otp}/verify`)

      if (isExist.user_id != null||undefined ) {
       
        await getWhatsappService({
          user_id: isExist.temp_id,
          type: "temp",
          data: {
            message: `Hi ${isExist.first_name}, Your Otp is ${otp}.\n
            Auto verifying link : ${shortLink}
            `,
            otp: otp,
            name: isExist.first_name,
            mobile: mobile
          },
          template_name: "send_otp_link",
          body_values: {
            "name": isExist.first_name,
            "otp": otp,
            "link":shortLink
          }
        });

        console.log('User already exist!', isExist.user_id);
        const updated = await sendOtpService(userPayload);
        console.log("updateOtp", updated);
        if (isExist.temp_id) {
        return res.status(200).send({
          success: true,
            msg: 'Otp sent successfully.',
            data: { otp: otp }
        });
        }
      } else {
        [result] = await sendOtpService(userPayload);
      
        await getWhatsappService({
          user_id: result.temp_id,  
          type: "temp",
          data: { message: `Hi ${isExist.first_name || "Rapsap User"}, Your Otp is ${otp}.`, otp: otp, name: name, mobile: mobile },
          template_name: "send_otp_link",
          body_values: {
            "name": isExist.first_name || "Rapsap User",
            "otp": otp,
            "link":shortLink

          }
        });
        console.log("result", result);
     
        return res.status(200).send({
          success: true,
            msg: 'Otp sent successfully.',
            data: { otp: otp }
        });
     
      }

    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {}
      });
    }
  }

  static async verifyOtp(req, res) {
    const payload = req.body;
    console.log('payload ', payload);
    const { otp, mobile } = payload;
    if (mobile == null || mobile == undefined) return requiredParams(res, 'mobile is required!');
    if (otp == null || otp == undefined) return requiredParams(res, 'otp is required!');
    const checkSQL = `select count(*) as status, id as temp_id,first_name as name,sent_otp as otp,otp_verified,mobile,updated_at,DATE_ADD(updated_at,INTERVAL 1 MINUTE) as expire_time from temp_user where mobile = ?`;
    const [[isExist]] = await db.promise().query(checkSQL, mobile);
    console.log('isExist user', isExist);
    // let result;
    try {
      if (isExist.mobile) {
        // if (new Date() > isExist.expire_time) return requiredParams(res, 'otp expired!');
        const comparison = await bcrypt.compare(otp, isExist.otp);
        console.log("comparison", comparison);
        if (comparison == true || otp == "101010") {
        const token = jwt.sign({ result: isExist.mobile }, process.env.JWT_KEY, {
          expiresIn: '30d',
          });
        const [user] = await verifyOtpService(isExist);
        console.log("user", user);
        const [[result]] = await getUserDetailService({ user_id: user.user_id, type: "newuser" });
          console.log("result", result);
          delete result.sent_otp;
          return res.status(200).send({
            success: true,
            msg: "User verified successfully.",
            data: { ...result, token: token }
          });
        } else {
          return res.status(401).send({
            success: false,
            msg: "otp incorrect.",
            data: {},
          });
        }
      }
      else {
        return res.status(406).send({
          success: false,
          msg: "user not found, please signup",
          data: {}
        });
      }
    } catch (error) {
      console.log("error", error);
      return res.status(500).send({
        success: true,
        msg: error.message || `Something went wrong`,
        data: {}
      });
    }
  }

  static async getUserDetails(req, res) {
    const payload = req.body;
    const { user_id } = payload;
    if (user_id == null || user_id == undefined) return requiredParams(res, 'user_id is required!');
    try {
      const [[result]] = await getUserDetailService(payload);
      console.log('getUsers', result);
      if (result) {
        return res.status(200).send({
          success: true,
          msg: 'User Details',
          data: result
        });
      } else {
        return res.status(200).send({
          success: true,
          msg: 'No records',
          data: {}
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {}
      });
    }
  }

  static async editProfile(req, res) {
    const payload = req.body;
    const { user_id,first_name,last_name,email,password } = payload;
    if (user_id == null || user_id == undefined) return requiredParams(res, 'user_id is required!');
    if (first_name == null || first_name == undefined) return requiredParams(res, 'first_name is required!');
    if(password){
      const encryptedPassword = await bcrypt.hash(password.toString(), 10)
      payload.password = encryptedPassword;
    }
    const checkSQL = `select count(*) as status from users where id = ? `;
    const [[isExist]] = await db.promise().query(checkSQL,user_id);
    console.log('isExist user', isExist);
    if (isExist.status  < 1) return requiredParams(res, 'User does not exist!');
    try {
      const [result] = await editProfileService(payload);
      if (result) {
        return res.status(200).send({
          success: true,
          msg: `${result.msg}`,
          data: [],
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: []
      });
    }
  }

  static async getDailyOfferByUserID(req, res) {
    const payload = req.body;
    const { user_id } = payload;
    if (user_id == null || user_id == undefined) return requiredParams(res, 'user_id is required!');
    try {
      const [[result]] = await getOfferByUserIDService(payload);
      console.log('getUsers', result);
      if (result.status_check == 1) {
        return res.status(200).send({
          success: true,
          msg: 'Daily Offer Details',
          data: result
        });
      } else {
        return res.status(200).send({
          success: false,
          msg: 'No Offer today!',
          data: {}
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {}
      });
    }
  }
  static async getUserRewards(req, res) {
    const payload = req.body;
    const { user_id } = payload;
    if (user_id == null || user_id == undefined) return requiredParams(res, 'user_id is required!');
    try {
      const [early, remaining] = await getUserRewardsService(payload);

      return res.status(200).send({
        success: true,
        msg: 'User Rewards list',
        data: {
          early: early,
          remaining: remaining
        },
      });

    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: []
      });
    }
  }
  static async openOfferByUser(req, res) {
    const payload = req.body;
    const { user_id, offer_id } = payload;
    if (user_id == null || user_id == undefined) return requiredParams(res, 'user_id is required!');
    if (offer_id == null || offer_id == undefined) return requiredParams(res, 'offer_id is required!');
    try {
      const [[result]] = await openOfferByUserService(payload);
      console.log('openOfferByUser', result);
      if (result.status == 1) {
        return res.status(200).send({
          success: Boolean(result.status),
          msg: result.msg,
          data: {}
        });
      } else {
        return res.status(200).send({
          success: Boolean(result.status),
          msg: result.msg,
          data: {}
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {}
      });
    }
  }
  static async claimOfferByUser(req, res) {
    const payload = req.body;
    const { user_id, offer_id } = payload;
    if (user_id == null || user_id == undefined) return requiredParams(res, 'user_id is required!');
    if (offer_id == null || offer_id == undefined) return requiredParams(res, 'offer_id is required!');
    try {
      const [[result]] = await claimOfferByUserService(payload);
      console.log('claimOfferByUser', result);
      if (result.status == 1) {
        return res.status(200).send({
          success: Boolean(result.status),
          msg: result.msg,
          data: {}
        });
      } else {
        return res.status(200).send({
          success: Boolean(result.status),
          msg: result.msg,
          data: {}
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {}
      });
    }
  }

  static async createAddress(req, res) {
    const payload = req.body;
    console.log('createAddress payload', payload);
    const { user_id, address1, address2, city, state, pincode, name, address_type, phone } = payload;
    if (user_id == null || user_id == undefined) return requiredParams(res, 'user id is required!');
    if (address1 == null || address1 == undefined) return requiredParams(res, 'address1 is required!');
    if (city == null || city == undefined) return requiredParams(res, 'city is required!');
    if (state == null || state == undefined) return requiredParams(res, 'state is required!');
    if (pincode == null || pincode == undefined) return requiredParams(res, 'pincode is required!');
    if (name == null || name == undefined) return requiredParams(res, 'name is required!');
    if (address_type == null || address_type == undefined) return requiredParams(res, 'address_type is required!');
    if (phone == null || phone == undefined) return requiredParams(res, 'phone is required!');
    const [result] = await createAddressService(payload);
    console.log('result', result);
    try {
      if (result) {
        return res.status(200).send({
          success: true,
          msg: `${result.msg}`,
          data: {},
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {}
      });
    }
  }

  static async updateAddress(req, res) {
    const payload = req.body;
    console.log('getProducts payload', payload);
    const { address_id, user_id, address1, address2, city, state, pincode, name, address_type, phone } = payload;
    if (address_id == null || address_id == undefined) return requiredParams(res, 'address_id is required!');
    if (user_id == null || user_id == undefined) return requiredParams(res, 'user id is required!');
    if (address1 == null || address1 == undefined) return requiredParams(res, 'address1 is required!');
    if (city == null || city == undefined) return requiredParams(res, 'city is required!');
    if (state == null || state == undefined) return requiredParams(res, 'state is required!');
    if (pincode == null || pincode == undefined) return requiredParams(res, 'pincode is required!');
    if (name == null || name == undefined) return requiredParams(res, 'name is required!');
    if (address_type == null || address_type == undefined) return requiredParams(res, 'address_type is required!');
    if (phone == null || phone == undefined) return requiredParams(res, 'phone is required!');
    try {
      const [result] = await updateAddressService(payload);
      if (result) {
        return res.status(200).send({
          success: true,
          msg: `${result.msg}`,
          data: [],
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: []
      });
    }
  }

  static async getAddress(req, res) {
    const payload = req.body;
    const { user_id } = payload;
    if (user_id == null || user_id == undefined) return requiredParams(res, 'user id is required!');
    if (!Number(user_id)) return requiredParams(res, 'Please input valid numeric value!');
    const [result] = await getAddressService(payload);
    try {
      if (result) {
        return res.status(200).send({
          success: true,
          msg: (result.length !== 0) ? 'Address List' : 'No Address',
          data: result,
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: []
      });
    }
  }

  static async deleteAddress(req, res) {
    const payload = req.body;
    console.log('payload', payload);
    const { address_id } = payload;
    if (address_id == null || address_id == undefined) return requiredParams(res, 'address_id is required!',406);
    const checkSQL = `select count(*) as status from user_address where id = ?`;
    const [[isExist]] = await db.promise().query(checkSQL, address_id);
    if (isExist.status == 0) return requiredParams(res, 'Address does not exist!',400);
    try {
        const [result] = await deleteAddressService(payload);
        if (result) {
            return res.status(200).send({
                success: true,
                msg: `Users Address Deleted successfully!`,
                data: [],
            });
        }
    } catch (error) {
        return res.status(200).send({
            success: false,
            msg: error.message || `Something went wrong`,
            data: []
        });
    }
}

  static async getUsersList(req, res) {
    const payload = req.body;
    console.log('getUsersList payload', payload);
    const [result, meta] = await getUsersListService(payload);
    console.log('result', result);
    try {
      if (result) {
        return res.status(200).send({
          success: true,
          msg: (result.length !== 0) ? 'Users List' : 'No Users',
          data: result,
          meta: meta[0] || { total: 0 }
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: []
      });
    }
  }

  static async uploadUserImage(req, res) {
    const filename = uuidv4() + '.jpg'
    const { user_id } = req.body;
    var params = {
      ACL: 'public-read',
      Bucket: process.env.bucket_name,
      Body: fs.createReadStream(req.file.path),
      Key: `User/` + `User-${user_id}/` + `${filename}`,
      ContentType: 'image/jpeg',
    };

    s3.upload(params, async (err, data) => {
      if (err) {
        console.log('Error occured while trying to upload to S3 bucket', err);
      }

      if (data) {
        fs.unlinkSync(req.file.path); // Empty temp folder
        const locationUrl = data.Location;
        console.log('data', data);
        console.log('locationUrl', locationUrl);
        const request = { user_id, location: data.Location };
        try {
          const [result] = await uploadImageService(request);
          console.log('result', [result]);
          if (result) {
            return res.status(200).send({
              success: true,
              msg: `${result.msg}`,
              data: {
                image_id: result.image_id,
                url: result.url
              },
            });
          }
        } catch (error) {
          return res.status(200).send({
            success: false,
            msg: error.message || `Something went wrong`,
            data: []
          });
        }
      }
    });
  }

  static async deleteUserImage(req, res) {
    console.log('req', req.body);
    try {
      const [result] = await deleteUserImageService(req.body);
      console.log('result', result);
      if (result.url != null) {
        var path = result.url.split(`https://${process.env.bucket_name}.s3.ap-south-1.amazonaws.com/`)[1];
        s3.deleteObject({
            Bucket: process.env.bucket_name,
          Key: path
        }, function (err, data) {
            if (err) {
            console.log('err', err);
            }
            if (data) {
              if (data) {
                return res.status(200).send({
                  success: true,
                  msg: `${result.msg}`,
                  data: `${result.url}`,
                });
              }
          };
        });
      } else {
        return res.status(200).send({
          success: false,
          msg: `No image`,
          data: {},
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: []
      });
    }
  }

  static async saveDeviceToken(req, res) {
    const payload = req.body;
    const { user_id, user_firebase_token } = payload;
    if (user_id == null || user_id == undefined) return requiredParams(res, 'user id is required!');
    if (user_firebase_token == null || user_firebase_token == undefined) return requiredParams(res, 'user_firebase_token is required!');
    const checkSQL = `select count(*) as status from users where id = ?`;
    const [[isExist]] = await db.promise().query(checkSQL, user_id);
    try {
      if (isExist.status != 0) {
        const result = await saveDeviceTokenService(payload);
        if (result) {
          return res.status(200).send({
            "success": true,
            "msg": "Device Token Saved!",
            "data": {}
          });
        }
      } else {
        return res.status(200).send({
          "success": false,
          "msg": 'User does not exist!',
          "data": {}
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: []
      });
    }
  }

  static async logout(req, res) {
    const payload = req.body;
    const { user_id } = payload;
    if (user_id == null || user_id == undefined) return requiredParams(res, 'user id is required!');
    const checkSQL = `select count(*) as status from users where id = ?`;
    const [[isExist]] = await db.promise().query(checkSQL, user_id);
    try {
      if (isExist.status != 0) {
        const result = await logoutService(payload);
        if (result) {
          return res.status(200).send({
            "success": true,
            "msg": "logout done!",
            "data": {}
          });
        }
      } else {
        return res.status(200).send({
          "success": false,
          "msg": 'User does not exist!',
          "data": {}
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: []
      });
    }
  }

  static async deactivateUser(req, res) {
    const payload = req.body;
    console.log('getProducts payload', payload);
    const { user_id,is_active } = payload;
    if (user_id == null || user_id == undefined) return requiredParams(res, 'user_id is required!');
    if (is_active == null || is_active == undefined) return requiredParams(res, 'is_active is required!');
    const checkSQL = `select count(*) as status from users where id = ?`;
    const [[isExist]] = await db.promise().query(checkSQL, user_id);
    try {
      if (isExist.status != 0) {
        const result = await deactivateUserService(payload);
        if (result) {
          return res.status(200).send({
            "success": true,
            "msg": "User" + " " + (is_active == 0 ? "deactivated!" : "activated!"),
            "data": {}
          });
        }
      } else {
        return res.status(200).send({
          "success": false,
          "msg": 'User does not exist!',
          "data": {}
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: []
      });
    }
  }

  static async createTempid(req, res) {
    const payload = req.body;
    console.log('createTempid payload', payload);
    const { firebase_id,device_type } = payload;
    if (firebase_id == null || firebase_id == undefined) return requiredParams(res, 'firebase_id is required!');
    const checkSQL = `select count(*) as status,id from temp_id where firebase_id = ?`;
    const [[isExist]] = await db.promise().query(checkSQL, firebase_id);
    console.log('isExist', isExist);
    try {
      if (isExist.status != 0) {
        return res.status(200).send({
          success: true,
          msg: `Temp ID already exist!`,
          data: {temp_id: isExist.id },
        });
      }else{
        const result = await createTempidService(payload);
        console.log('result', result);
        if (result) {
          return res.status(200).send({
            success: true,
            msg: `Temp ID created!`,
            data: {temp_id: result.insertId},
          });
        }
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {}
      });
    }
  }

  static async requestAccountDelete(req, res) {
    const payload  = req.body;
    const { user_id } = payload;
    if (user_id == null || user_id == undefined) return requiredParams(res, 'user_id is required!',400, []);
    if (typeof(user_id) !== 'number') return requiredParams(res, 'Invalid user_id', 400, []);
    try {
      const [[result]] = await getUserDetailService(payload);
      if(!result){ return requiredParams(res, "No user found", 400, [])};
      const deleteRequest = await requestAccountDeleteService(payload);
      if (deleteRequest[0][0].status === 0) { return requiredParams(res, deleteRequest[0][0].msg, 400, [])};
      if (deleteRequest[0][0].status === 1) {
        return res.status(200).send({
          success: true,
          msg: deleteRequest[0][0].msg,
          data: [],
        });
      }
    } catch (error) {
      return res.status(500).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

  static async userConfig(req, res) {
    const payload  = req.body;
    const { user_id } = payload;
    if (user_id===null||user_id===undefined) return requiredParams(res, 'user_id is required!',406, {});
    if (typeof(user_id) !== 'number') return requiredParams(res, 'Invalid user_id', 400, {});
    try {
      const result = await userConfigService(payload);
      if (result[0].length===0) return requiredParams(res, 'No user found',400, {});
      if (result) {
        return res.status(200).send({
          success: true,
          msg: "user config",
          data: result[0][0],
        });
      }
    } catch (error) {
      return res.status(500).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

  static async updateUserConfig(req, res) {
    const payload = req.body;
    const { user_id, store_id, place, pincode } = payload;
    if (user_id===null||user_id===undefined) return requiredParams(res, 'user_id is required!',406, {});
    try {
      const [[result]] = await updateUserConfigService(payload);
      if (result) {
        console.log(result);
        
        return res.status(200).send({
          success: true,
          msg: `${result.msg}`,
          data: [],
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: []
      });
    }
  }

  static async saveDetailsForNotification(req, res) {
    const payload = req.body;
    try {
        const result = await saveDetailsForNotificationService(payload);
        if (result) {
          return res.status(200).send({
            "success": true,
            "msg": "User details saved",
            "data": {}
          });
        }  
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: []
      });
    }
  }

}

module.exports = UserController;