
const { getnotificationService, getWhatsappService} = require("../../services/NotificationServices/NotificationServices");

class UserController {
  static async getnotification(req, res) {
    try {
      const response = await getnotificationService(req.body);
      console.log('message id', response);
      if (response) {
        return res.status(200).send({
          success: true,
          msg: `Notification Sent!`,
          data: response
        });
      } else {
        return res.status(200).send({
          success: false,
          msg: `Notification Error!`,
          data: response
        });
      }
    } catch (error) {
      console.log('error', error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {}
      });
    }
  }
  

  static async getWhatsappNotification(req, res) {
    try {
      const response = await getWhatsappService(req.body);
      console.log('message id', response);
      if (response) {
        return res.status(200).send({
          success: true,
          msg: `Notification Sent!`,
          data: response
        });
      } else {
        return res.status(200).send({
          success: false,
          msg: `Notification Error!`,
          data: response
        });
      }
    } catch (error) {
      console.log('error', error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {}
      });
    }
  }

}

module.exports = UserController;