const { createCategoryService, updateCategoryService, getCategoryService } = require("../../services/EcommServices/CategoryService");
const { requiredParams } = require('../../utility/requiredCheck');
const aws = require('aws-sdk');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const { uploadImageService, deleteImageService } = require('../../services/EcommServices/ProductService');
aws.config.setPromisesDependency();
aws.config.update({
  accessKeyId: process.env.accessKeyId,
  secretAccessKey: process.env.secretAccessKey,
  region: process.env.region
});
const s3 = new aws.S3();

class CategoryController {
  static async createCategory(req, res) {
    const payload = req.body;
    const { name, description } = payload;
    //console.log('createCategory payload', payload);
    if (name == null || name == undefined) return requiredParams(res, 'name is required!');
    const [result] = await createCategoryService(payload);
    //console.log('result', result);
    try {
      if (result) {
        return res.status(200).send({
          success: true,
          msg: `${result.msg}`,
          data: { category_id: result.category_id }
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: []
      });
    }
  }

  static async updateCategory(req, res) {
    const payload = req.body;
    const { category_id, name, description } = payload;
    if (category_id == null || category_id == undefined) return requiredParams(res, 'category_id is required!');
    if (name == null || name == undefined) return requiredParams(res, 'name is required!');
    if (description === undefined) return requiredParams(res, 'description is required!');
    //console.log('updateCategory payload', payload);
    const [result] = await updateCategoryService(payload);
    //console.log('result', result);
    try {
      if (result) {
        return res.status(200).send({
          success: true,
          msg: `${result.msg}`,
          data: []
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: []
      });
    }
  }

  static async getCategory(req, res) {
    const payload = req.body;
    //console.log('createAddress payload', payload);
    const [result, meta] = await getCategoryService(payload);
    //console.log('result', result);
    try {
      if (result) {
        return res.status(200).send({
          success: true,
          msg: (result.length !== 0) ? 'Category List' : 'No Category',
          data: result,
          meta: meta[0] || { total: 0 }
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: []
      });
    }
  }

  static async UploadCategoryImage(req, res) {
    const filename = uuidv4() + '.jpg'
    const { category_id } = req.body;
    if (category_id == null || category_id == undefined) return requiredParams(res, 'category_id is required!');
    var params = {
      ACL: 'public-read',
      Bucket: process.env.bucket_name,
      Body: fs.createReadStream(req.file.path),
      Key: `Category/` + `Category-${category_id}/` + `${filename}`,
      ContentType: 'image/jpeg',
    };

    s3.upload(params, async (err, data) => {
      if (err) {
        console.log('Error occured while trying to upload to S3 bucket', err);
      }

      if (data) {
        fs.unlinkSync(req.file.path); // Empty temp folder
        const locationUrl = data.Location;
        console.log('data', data);
        console.log('locationUrl', locationUrl);
        const request = { category_id, location: data.Location };
        try {
          const [result] = await uploadImageService(request);
          //console.log('result', [result]);
          if (result) {
            return res.status(200).send({
              success: true,
              msg: `${result.msg}`,
              data: {
                image_id: result.image_id,
                url: result.url
              },
            });
          }
        } catch (error) {
          return res.status(200).send({
            success: false,
            msg: error.message || `Something went wrong`,
            data: []
          });
        }
      }
    });
  }

  static async deleteCategoryImage(req, res) {
    try {
      const [result] = await deleteImageService(req.body);
      //console.log('result', result);
      if (result.url != null) {
        var path = result.url.split(`https://${process.env.bucket_name}.s3.ap-south-1.amazonaws.com/`)[1];
        s3.deleteObject({
          Bucket: process.env.bucket_name,
          Key: path
        }, function (err, data) {
          if (err) {
            console.log('err', err);
          }
          if (data) {
            if (data) {
              return res.status(200).send({
                success: true,
                msg: `${result.msg}`,
                data: `${result.url}`,
              });
            }
          };
        });
      } else {
        return res.status(200).send({
          success: false,
          msg: `No image`,
          data: {},
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: []
      });
    }
  }

}

module.exports = CategoryController;