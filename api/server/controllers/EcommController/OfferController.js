const { createOfferService, updateOfferService, getOfferService, getHomePageOfferService, createBannerService, getBannerService, updateBannerService } = require("../../services/EcommServices/OfferService");
const { requiredParams } = require('../../utility/requiredCheck');
const aws = require('aws-sdk');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const { uploadImageService, deleteImageService } = require("../../services/EcommServices/ProductService");
const { deleteUserImageService } = require("../../services/UserService/UserService");
aws.config.setPromisesDependency();
aws.config.update({
    accessKeyId: process.env.accessKeyId,
    secretAccessKey: process.env.secretAccessKey,
    region: process.env.region
});
const s3 = new aws.S3();
const db = require('../../../config/connection');
class OfferController {
    static async createOffer(req, res) {
        const payload = req.body;
        const { name, coupon_code, valid_from, valid_till, is_percent, percent_discount, amount_discount } = payload;
        //console.log('createCategory payload', payload);
        if (name == null || name == undefined) return requiredParams(res, 'name is required!');
        if (is_percent == 1) {
            if (percent_discount == null || percent_discount == undefined) return requiredParams(res, 'percent_discount is required!');
        }
        if (is_percent == 0) {
            if (amount_discount == null || amount_discount == undefined) return requiredParams(res, 'amount_discount is required!');
        }
        if (coupon_code == null || coupon_code == undefined) return requiredParams(res, 'coupon_code is required!');
        if (valid_from == null || valid_from == undefined) return requiredParams(res, 'valid_from is required!');
        if (valid_till == null || valid_till == undefined) return requiredParams(res, 'valid_till is required!');
        const [result] = await createOfferService(payload);
        //console.log('result', result);
        try {
            if (result) {
                return res.status(200).send({
                    success: true,
                    msg: `${result.msg}`,
                    data: []
                });
            }
        } catch (error) {
            return res.status(200).send({
                success: false,
                msg: error.message || `Something went wrong`,
                data: []
            });
        }
    }

    static async updateOffer(req, res) {
        const payload = req.body;
        const { name, description, coupon_code, valid_from, valid_till, is_percent, percent_discount, amount_discount } = payload;
        //console.log('updateOffer payload', payload);
        if (name == null || name == undefined) return requiredParams(res, 'name is required!');
        // if (description == null || description == undefined) return requiredParams(res, 'description is required!');
        if (is_percent == 1) {
            if (percent_discount == null || percent_discount == undefined) return requiredParams(res, 'percent_discount is required!');
        }
        if (is_percent == 0) {
            if (amount_discount == null || amount_discount == undefined) return requiredParams(res, 'amount_discount is required!');
        }
        if (coupon_code == null || coupon_code == undefined) return requiredParams(res, 'coupon_code is required!');
        if (valid_from == null || valid_from == undefined) return requiredParams(res, 'valid_from is required!');
        if (valid_till == null || valid_till == undefined) return requiredParams(res, 'valid_till is required!');
        const [result] = await updateOfferService(payload);
        //console.log('result', result);
        try {
            if (result) {
                return res.status(200).send({
                    success: true,
                    msg: `${result.msg}`,
                    data: []
                });
            }
        } catch (error) {
            return res.status(200).send({
                success: false,
                msg: error.message || `Something went wrong`,
                data: []
            });
        }
    }

    static async getOffer(req, res) {
        const payload = req.body;
        //console.log('getOffer payload', payload);
        const [result, meta] = await getOfferService(payload);
        //console.log('result', result);
        try {
            if (result) {
                return res.status(200).send({
                    success: true,
                    msg: (result.length !== 0) ? 'Offer List' : 'No Offer',
                    data: result,
                    meta: meta[0] || { total: 0 }
                });
            }
        } catch (error) {
            return res.status(200).send({
                success: false,
                msg: error.message || `Something went wrong`,
                data: []
            });
        }
    }

    static async getHomePageOffer(req, res) {
        const payload = req.body;
        //console.log('getOffer payload', payload);
        const [result, meta] = await getHomePageOfferService(payload);
        //console.log('result', result);
        try {
            if (result) {
                return res.status(200).send({
                    success: true,
                    msg: (result.length !== 0) ? 'Home Page Offer List' : 'No Offer',
                    data: result,
                    meta: meta[0] || { total: 0 }
                });
            }
        } catch (error) {
            return res.status(200).send({
                success: false,
                msg: error.message || `Something went wrong`,
                data: []
            });
        }
    }

    static async uploadOfferImage(req, res) {
        const filename = uuidv4() + '.jpg'
        const { offer_id } = req.body;
        var params = {
            // ACL: 'public-read',
            Bucket: process.env.bucket_name,
            Body: fs.createReadStream(req.file.path),
            Key: `Offer-${offer_id}/` + `${filename}`,
            ContentType: 'image/jpeg',
        };

        s3.upload(params, async (err, data) => {
            if (err) {
                console.log('Error occured while trying to upload to S3 bucket', err);
            }

            if (data) {
                fs.unlinkSync(req.file.path); // Empty temp folder
                const locationUrl = data.Location;
                console.log('data', data);
                console.log('locationUrl', locationUrl);
                const request = { offer_id, location: data.Location };
                try {
                    const [result] = await uploadImageService(request);
                    //console.log('result', [result]);
                    if (result) {
                        return res.status(200).send({
                            success: true,
                            msg: `${result.msg}`,
                            data: {
                                image_id: result.image_id,
                                url: result.url
                            },
                        });
                    }
                } catch (error) {
                    return res.status(200).send({
                        success: false,
                        msg: error.message || `Something went wrong`,
                        data: []
                    });
                }
            }
        });
    }

    static async deleteOfferImage(req, res) {
        //console.log('req', req.body);
        try {
            const [result] = await deleteUserImageService(req.body);
            //console.log('result', result);
            if (result.url != null) {
                var path = result.url.split(`https://${process.env.bucket_name}.s3.ap-south-1.amazonaws.com/`)[1];
                s3.deleteObject({
                    Bucket: process.env.bucket_name,
                    Key: path
                }, function (err, data) {
                    if (err) {
                        console.log('err', err);
                    }
                    if (data) {
                        if (data) {
                            return res.status(200).send({
                                success: true,
                                msg: `${result.msg}`,
                                data: `${result.url}`,
                            });
                        }
                    };
                });
            } else {
                return res.status(200).send({
                    success: false,
                    msg: `No image`,
                    data: {},
                });
            }
        } catch (error) {
            return res.status(200).send({
                success: false,
                msg: error.message || `Something went wrong`,
                data: []
            });
        }
    }

    static async createBanner(req, res) {
        const payload = req.body;
        const { type, product_id, url, category_id, sub_category_id } = payload;
        if (type == null || type == undefined) return requiredParams(res, 'type is required!');
        if (type == "url") {
            if (url == null || url == undefined) return requiredParams(res, 'url is required!');
        }
        if (type == "product") {
            if (product_id == null || product_id == undefined) return requiredParams(res, 'product_id is required!');
        }
        if (type == "category") {
            if (category_id == null || category_id == undefined) return requiredParams(res, 'category_id is required!');
        }
        if (type == "sub_category") {
            if (sub_category_id == null || sub_category_id == undefined) return requiredParams(res, 'sub_category_id is required!');
        }
        const [result] = await createBannerService(payload);
        //console.log('result', result);
        try {
            if (result) {
                return res.status(200).send({
                    success: true,
                    msg: `${result.msg}`,
                    data: `${result.banner_id}`
                });
            }
        } catch (error) {
            return res.status(200).send({
                success: false,
                msg: error.message || `Something went wrong`,
                data: []
            });
        }
    }

    static async updateBanner(req, res) {
        const payload = req.body;
        const { type, banner_id, url, product_id, category_id, sub_category_id  } = payload;
        if (banner_id == null || banner_id == undefined) return requiredParams(res, 'banner_id is required!');
        if (type == null || type == undefined) return requiredParams(res, 'type is required!');
        if (type == "url") {
            if (url == null || url == undefined) return requiredParams(res, 'url is required!');
        }
        if (type == "product") {
            if (product_id == null || product_id == undefined) return requiredParams(res, 'product_id is required!');
        }
        if (type == "category") {
            if (category_id == null || category_id == undefined) return requiredParams(res, 'category_id is required!');
        }
        if (type == "sub_category") {
            if (sub_category_id == null || sub_category_id == undefined) return requiredParams(res, 'sub_category_id is required!');
        }
        const [result] = await updateBannerService(payload);
        try {
            if (result) {
                return res.status(200).send({
                    success: true,
                    msg: `${result.msg}`,
                    data: []
                });
            }
        } catch (error) {
            return res.status(200).send({
                success: false,
                msg: error.message || `Something went wrong`,
                data: []
            });
        }
    }

    static async uploadBannerImage(req, res) {
        const filename = uuidv4() + '.jpg'
        const { banner_id } = req.body;
        var params = {
            ACL: 'public-read',
            Bucket: process.env.bucket_name,
            Body: fs.createReadStream(req.file.path),
            Key: `Banners/` + `Banner-${banner_id}/` + `${filename}`,
            ContentType: 'image/jpeg',
        };

        s3.upload(params, async (err, data) => {
            if (err) {
                console.log('Error occured while trying to upload to S3 bucket', err);
            }

            if (data) {
                fs.unlinkSync(req.file.path); // Empty temp folder
                const locationUrl = data.Location;
                console.log('data', data);
                console.log('locationUrl', locationUrl);
                const request = { banner_id, location: data.Location };
                try {
                    const [result] = await uploadImageService(request);
                    //console.log('result', [result]);
                    if (result) {
                        return res.status(200).send({
                            success: true,
                            msg: `${result.msg}`,
                            data: {
                                image_id: result.image_id,
                                url: result.url
                            },
                        });
                    }
                } catch (error) {
                    return res.status(200).send({
                        success: false,
                        msg: error.message || `Something went wrong`,
                        data: []
                    });
                }
            }
        });
    }

    static async deleteBannerImage(req, res) {
        const { banner_image_id } = req.body;
        try {
          const [result] = await deleteImageService(req.body);
          console.log('result', result);
          if (result.url != null) {
            let baseUrl = `https://` + process.env.bucket_name + `.s3.ap-south-1.amazonaws.com/`;
            var path = result.url.split(baseUrl)[1];
            console.log('bucket', process.env.bucket_name);
            console.log('path', path);
            s3.deleteObject({
              Bucket: process.env.bucket_name,
              Key: path
            }, function (err, data) {
              if (err) {
                console.log('err', err);
              }
              if (data) {
                if (data) {
                  return res.status(200).send({
                    success: true,
                    msg: `${result.msg}`,
                    data: `${result.url}`,
                  });
                }
              };
            });
          } else {
            return res.status(200).send({
              success: false,
              msg: `No image`,
              data: {},
            });
          }
        } catch (error) {
          return res.status(200).send({
            success: false,
            msg: error.message || `Something went wrong`,
            data: []
          });
        }
      }

    static async getBanner(req, res) {
        const payload = req.body;
        const [result, meta] = await getBannerService(payload);
        console.log('log',result);
        try {
            if (result) {
                return res.status(200).send({
                    success: true,
                    msg: (result.length !== 0) ? 'Banner List' : 'No Banner',
                    data: result,
                    meta: meta[0] || { total: 0 }
                });
            }
        } catch (error) {
            return res.status(200).send({
                success: false,
                msg: error.message || `Something went wrong`,
                data: []
            });
        }
    }

}

module.exports = OfferController;