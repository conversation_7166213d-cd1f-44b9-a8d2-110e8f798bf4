const { createSubCategoryGroupService, updateSubCategoryGroupService, getSubCategoryGroupService } = require("../../services/EcommServices/SubCategoryGroupService");
const { uploadImageService, deleteImageService } = require("../../services/EcommServices/ProductService");
const { requiredParams } = require('../../utility/requiredCheck');
const aws = require('aws-sdk');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
aws.config.setPromisesDependency();
aws.config.update({
  accessKeyId: process.env.accessKeyId,
  secretAccessKey: process.env.secretAccessKey,
  region: process.env.region
});
const s3 = new aws.S3();

class SubCategoryGroupController {
    static async createSubCategoryGroup(req, res) {
        const payload = req.body;
        const { sub_category_id, name } = payload;
        //console.log('createCategory payload', payload);
        if (sub_category_id == null || sub_category_id == undefined) return requiredParams(res, 'sub_category_id is required!');
        if (name == null || name == undefined) return requiredParams(res, 'name is required!');
        const [result] = await createSubCategoryGroupService(payload);
        //console.log('result', result);
        try {
            if (result) {
                return res.status(200).send({
                    success: true,
                    msg: `${result.msg}`,
                    data: {
                        sub_category_group_id: result.sub_category_group_id
                    }
                });
            }
        } catch (error) {
            return res.status(200).send({
                success: false,
                msg: error.message || `Something went wrong`,
                data: []
            });
        }
    }

    static async updateSubCategoryGroup(req, res) {
        const payload = req.body;
        const { sub_category_group_id, name, description } = payload;
        if (sub_category_group_id == null || sub_category_group_id == undefined) return requiredParams(res, 'sub_category_group_id is required!');
        if (name == null || name == undefined) return requiredParams(res, 'name is required!');
        if (description === undefined) return requiredParams(res, 'description is required!');
        //console.log('updateSubCategoryGroup payload', payload);
        const [result] = await updateSubCategoryGroupService(payload);
        //console.log('result', result);
        try {
            if (result) {
                return res.status(200).send({
                    success: true,
                    msg: `${result.msg}`,
                    data: []
                });
            }
        } catch (error) {
            return res.status(200).send({
                success: false,
                msg: error.message || `Something went wrong`,
                data: []
            });
        }
    }

    static async getSubCategoryGroup(req, res) {
        const payload = req.body;
        //console.log('req.body payload', req.body);
        const { sub_category_id,category_id } = payload;
        //console.log('getSubCategoryGroup payload', payload);
        if (sub_category_id == null || sub_category_id == undefined) return requiredParams(res, 'sub_category_id is required!');
        const [result, meta] = await getSubCategoryGroupService(payload);
        //console.log('result', result);
        const All ={
            "sub_category_group_id": 0,
            "sub_category_id": sub_category_id,
            "sub_category_group_name": "All",
            "description": null,
            "category_id": category_id,
            "sub_category_name": "",
            "category_name": "",
            "sub_category_group_image": "",
            "image_id": null,
            "created_at": "2021-10-01T17:44:06.000Z",
            "updated_at": "2021-10-01T17:44:06.000Z"
        };
        let group = [];
        group.push(All);
        result.map((m,i) =>{
            group.push(m)
        });
        try {
            if (result) {
                return res.status(200).send({
                    success: true,
                    msg: (result.length !== 0) ? 'SubCategoryGroup List' : 'No SubCategoryGroup',
                    data: group,
                    meta: meta[0] || { total: 0 }
                });
            }
        } catch (error) {
            return res.status(200).send({
                success: false,
                msg: error.message || `Something went wrong`,
                data: []
            });
        }
    }

    static async UploadSubCategoryGroupImage(req, res) {
        const filename = uuidv4() + '.jpg'
        const { sub_category_group_id } = req.body;
        var params = {
          ACL: 'public-read',
          Bucket: process.env.bucket_name,
          Body: fs.createReadStream(req.file.path),
          Key: `SubcategoryGroup/` + `SubcategoryGroup-${sub_category_group_id}/` + `${filename}`,
          ContentType: 'image/jpeg',
        };
    
        s3.upload(params, async (err, data) => {
          if (err) {
            console.log('Error occured while trying to upload to S3 bucket', err);
            return res.status(200).send({
                success: false,
                msg: `Error occured while trying to upload to S3 bucket`,
                data: err,
              });
          }
          if (data) {
            fs.unlinkSync(req.file.path); // Empty temp folder
            const locationUrl = data.Location;
            console.log('data', data);
            console.log('locationUrl', locationUrl);
            const request = { sub_category_group_id, location: data.Location };
            try {
              const [result] = await uploadImageService(request);
              //console.log('result', [result]);
              if (result) {
                return res.status(200).send({
                  success: true,
                  msg: `${result.msg}`,
                  data: {
                    image_id: result.image_id,
                    url: result.url
                  },
                });
              }
            } catch (error) {
              return res.status(200).send({
                success: false,
                msg: error.message || `Something went wrong`,
                data: []
              });
            }
          }
        });
      }
    
      static async deleteSubCategoryGroupImage(req, res) {
        const { subcategory_group_image_id } = req.body;
        try {
          const [result] = await deleteImageService(req.body);
          console.log('result', result);
          if (result.url != null) {
            let baseUrl = `https://` + process.env.bucket_name + `.s3.ap-south-1.amazonaws.com/`;
            var path = result.url.split(baseUrl)[1];
            console.log('bucket', process.env.bucket_name);
            console.log('path', path);
            s3.deleteObject({
              Bucket: process.env.bucket_name,
              Key: path
            }, function (err, data) {
              if (err) {
                console.log('err', err);
              }
              if (data) {
                if (data) {
                  return res.status(200).send({
                    success: true,
                    msg: `${result.msg}`,
                    data: `${result.url}`,
                  });
                }
              };
            });
          } else {
            return res.status(200).send({
              success: false,
              msg: `No image`,
              data: {},
            });
          }
        } catch (error) {
          return res.status(200).send({
            success: false,
            msg: error.message || `Something went wrong`,
            data: []
          });
        }
      }
    


}

module.exports = SubCategoryGroupController;