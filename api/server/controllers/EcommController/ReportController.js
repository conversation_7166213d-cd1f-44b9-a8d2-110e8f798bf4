const { exportCsvReportService, exportVaraintCsvService } = require("../../services/EcommServices/ReportService");
const { requiredParams } = require('../../utility/requiredCheck');
const db = require('../../../config/connection');

class ReportController {

  static async exportCsvReport(req, res) {
    const payload = req.body;
    const { order_id, type, start_date, end_date } = payload;
    if (type == null || type == undefined) return requiredParams(res, 'type is required!');
    if (type === 'date') {
      if (start_date == null || start_date == undefined) return requiredParams(res, 'start_date is required!');
    }
    else if (type === 'id') {
      if (order_id == null || order_id == undefined) return requiredParams(res, 'order_ids is required!');
    }
    else {
      return requiredParams(res, 'Invalid type');
    }
    try {
      const [result] = await exportCsvReportService(payload);
      if (result) {
        return res.status(200).send({
          success: true,
          msg: (result.length !== 0) ? 'Order exports list' : 'No records',
          data: result,
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: []
      });
    }
  }

  static async exportVaraintCsv(req, res) {
    const payload = req.body;
    try {
      if (payload.store_id != null || payload.store_id != undefined) {
        const sql = `select count(*) as count  from storewise_variants sv where sv.store_id = ${payload.store_id}`;
        const [[check]] = await db.promise().query(sql);
        console.log('check', check.count);
        if (check.count == 0) {
          return res.status(200).send({
            success: false,
            msg: 'No records for this store',
            data: [],
          });
        }
      }
      const [result] = await exportVaraintCsvService(payload);
      if (result) {
        return res.status(200).send({
          success: true,
          msg: (result.length !== 0) ? 'Variants exports list' : 'No records',
          data: result,
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: []
      });
    }
  }

}

module.exports = ReportController;