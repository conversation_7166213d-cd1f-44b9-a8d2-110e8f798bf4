const { registerWebhookService, getWebhookService } = require("../../services/DashboardServices/DashboardService");
const { placeDeliveryOrderService, cancelDeliveryOrderService, callbackDeliveryStatusService } = require("../../services/EcommServices/DeliveryService");
const { getnotificationService } = require("../../services/NotificationServices/NotificationServices");

const { requiredParams } = require('../../utility/requiredCheck');

class DeliveryController {

  static async placeDeliveryOrder(req, res) {
    const payload = req.body;
    const { order_id, store_code, drop_instruction_text } = payload;
    if (order_id == null || order_id == undefined) return requiredParams(res, 'order_id is required!');
    if (store_code == null || store_code == undefined) return requiredParams(res, 'store_code is required!');
    if (drop_instruction_text == null || drop_instruction_text == undefined) return requiredParams(res, 'drop_instruction_text is required!');
    const result = await placeDeliveryOrderService(payload);
    console.log('result', result);
    try {
      if (result) {
        return res.status(200).send({
          success: Boolean(result.status),
          msg: 'Delivery Order Placed Successfully',
          data: {
            track_url: result.track_url,
            sfx_order_id: result.sfx_order_id,
            order_id: result.order_id
          }
        });
      } else {
        return res.status(200).send({
          success: false,
          msg: 'Unable to place order or its existing order, contact support!',
          data: {}
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {}
      });
    }
  }

  static async cancelDeliveryOrder(req, res) {
    const payload = req.body;
    const { cancel_reason, cancelled_by, order_id } = payload;
    if (cancel_reason == null || cancel_reason == undefined) return requiredParams(res, 'cancel_reason is required!');
    if (cancelled_by == null || cancelled_by == undefined) return requiredParams(res, 'cancelled_by is required!');
    if (order_id == null || order_id == undefined) return requiredParams(res, 'order_id is required!');

    const result = await cancelDeliveryOrderService(payload);
    console.log('result', result);
    try {
      if (result) {
        return res.status(200).send({
          success: result.status,
          msg: result.msg,
          data: result.data
        });
      } else {
        return res.status(200).send({
          success: false,
          msg: 'Unable to place order or its existing order, contact support!',
          data: {}
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {}
      });
    }
  }

  static async callbackDeliveryStatus(req, res) {
    const payload = req.body;
    const { client_order_id, order_status } = payload

    const [result] = await callbackDeliveryStatusService(payload);
    console.log('result', result);
    try {
      if (result) {
        if(order_status == 'packed'){
          await getnotificationService({receiver_id :result.user_id, data:{title : "ORDER PACKED", body :"Your order is packed.", type : "order", "order_id":JSON.stringify(client_order_id) }}); 
        }
        if(order_status == 'delivered'){
          await getnotificationService({receiver_id :result.user_id, data:{title : "ORDER DELIVERED", body :"Your order is delivered.", type : "order", "order_id":JSON.stringify(client_order_id) }}); 
        }
        return res.status(200).send({
          success: result.status,
          msg: result.msg,
          // data: result.data
        });
      } else {
        return res.status(200).send({
          success: false,
          msg: 'Unable to place order or its existing order, contact support!',
          data: {}
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {}
      });
    }
  }

  static async registerWebhook(req, res) {
    const payload = req.body;

    const [[result]] = await registerWebhookService(payload);
    console.log('result', result);
    try {
      if (result) {
       
        return res.status(200).send({
          success: Boolean(result.status),
          msg: result.msg,
          data: {}
        });
      }
      else{
          
        return res.status(200).send({
          success: false,
          msg: 'Error ',
          data: {}
        });

      }

    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {}
      });
    }
  }

  static async getWebhook(req, res) {

    const  payload=req.body;

    const result = await getWebhookService(payload);
    console.log('result', result);
    try {
      if (result) {
        

        return res.status(200).send({
          success: true,
          msg: result==JSON.stringify(result) === '[]'?"No webhooks":"webhooks found",
          data: result
        });
      }
      else{
          
        return res.status(200).send({
          success: false,
          msg: 'No webhooks ',
          data: {}
        });

      }

    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {}
      });
    }
  }



}





module.exports = DeliveryController;