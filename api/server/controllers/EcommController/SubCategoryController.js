const { createSubCategoryService, updateSubCategoryService, getSubCategoryService } = require("../../services/EcommServices/SubCategoryService");
const { uploadImageService, deleteImageService } = require("../../services/EcommServices/ProductService");
const { requiredParams } = require('../../utility/requiredCheck');
const aws = require('aws-sdk');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
aws.config.setPromisesDependency();
aws.config.update({
  accessKeyId: process.env.accessKeyId,
  secretAccessKey: process.env.secretAccessKey,
  region: process.env.region
});
const s3 = new aws.S3();

class SubCategoryController {
    static async createSubCategory(req, res) {
        const payload = req.body;
        const { category_id, name } = payload;
        //console.log('createCategory payload', payload);
        if (category_id == null || category_id == undefined) return requiredParams(res, 'category_id is required!');
        if (name == null || name == undefined) return requiredParams(res, 'name is required!');
        const [result] = await createSubCategoryService(payload);
        //console.log('result', result);
        try {
            if (result) {
                return res.status(200).send({
                    success: true,
                    msg: `${result.msg}`,
                    data: {
                        sub_category_id: result.sub_category_id
                    }
                });
            }
        } catch (error) {
            return res.status(200).send({
                success: false,
                msg: error.message || `Something went wrong`,
                data: []
            });
        }
    }

    static async updateSubCategory(req, res) {
        const payload = req.body;
        const { sub_category_id, name, description } = payload;
        if (sub_category_id == null || sub_category_id == undefined) return requiredParams(res, 'sub_category_id is required!');
        if (name == null || name == undefined) return requiredParams(res, 'name is required!');
        if (description === undefined) return requiredParams(res, 'description is required!');
        //console.log('updateSubCategory payload', payload);
        const [result] = await updateSubCategoryService(payload);
        //console.log('result', result);
        try {
            if (result) {
                return res.status(200).send({
                    success: true,
                    msg: `${result.msg}`,
                    data: []
                });
            }
        } catch (error) {
            return res.status(200).send({
                success: false,
                msg: error.message || `Something went wrong`,
                data: []
            });
        }
    }

    static async getSubCategory(req, res) {
        const payload = req.body;
        //console.log('req.body payload', req.body);
        //console.log('getSubCategory payload', payload);
        const [result, meta] = await getSubCategoryService(payload);
        //console.log('result', result);
        try {
            if (result) {
                return res.status(200).send({
                    success: true,
                    msg: (result.length !== 0) ? 'SubCategory List' : 'No SubCategory',
                    data: result,
                    meta: meta[0] || { total: 0 }
                });
            }
        } catch (error) {
            return res.status(200).send({
                success: false,
                msg: error.message || `Something went wrong`,
                data: []
            });
        }
    }

    static async UploadSubCategoryImage(req, res) {
        const filename = uuidv4() + '.jpg'
        const { sub_category_id } = req.body;
        var params = {
          ACL: 'public-read',
          Bucket: process.env.bucket_name,
          Body: fs.createReadStream(req.file.path),
          Key: `Subcategory/` + `Subcategory-${sub_category_id}/` + `${filename}`,
          ContentType: 'image/jpeg',
        };
    
        s3.upload(params, async (err, data) => {
          if (err) {
            console.log('Error occured while trying to upload to S3 bucket', err);
            return res.status(200).send({
                success: false,
                msg: `Error occured while trying to upload to S3 bucket`,
                data: err,
              });
          }
          if (data) {
            fs.unlinkSync(req.file.path); // Empty temp folder
            const locationUrl = data.Location;
            console.log('data', data);
            console.log('locationUrl', locationUrl);
            const request = { sub_category_id, location: data.Location };
            try {
              const [result] = await uploadImageService(request);
              //console.log('result', [result]);
              if (result) {
                return res.status(200).send({
                  success: true,
                  msg: `${result.msg}`,
                  data: {
                    image_id: result.image_id,
                    url: result.url
                  },
                });
              }
            } catch (error) {
              return res.status(200).send({
                success: false,
                msg: error.message || `Something went wrong`,
                data: []
              });
            }
          }
        });
      }
    
      static async deleteSubCategoryImage(req, res) {
        const { subcategory_image_id } = req.body;
        try {
          const [result] = await deleteImageService(req.body);
          console.log('result', result);
          if (result.url != null) {
            let baseUrl = `https://` + process.env.bucket_name + `.s3.ap-south-1.amazonaws.com/`;
            var path = result.url.split(baseUrl)[1];
            console.log('bucket', process.env.bucket_name);
            console.log('path', path);
            s3.deleteObject({
              Bucket: process.env.bucket_name,
              Key: path
            }, function (err, data) {
              if (err) {
                console.log('err', err);
              }
              if (data) {
                if (data) {
                  return res.status(200).send({
                    success: true,
                    msg: `${result.msg}`,
                    data: `${result.url}`,
                  });
                }
              };
            });
          } else {
            return res.status(200).send({
              success: false,
              msg: `No image`,
              data: {},
            });
          }
        } catch (error) {
          return res.status(200).send({
            success: false,
            msg: error.message || `Something went wrong`,
            data: []
          });
        }
      }
    


}

module.exports = SubCategoryController;