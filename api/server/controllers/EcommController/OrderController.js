const { cancelDeliveryOrderService } = require("../../services/EcommServices/DeliveryService");
const { getnotificationService, CallWebhook } = require("../../services/NotificationServices/NotificationServices");
const { createOrderService, getOrderService, getcartService, getOrderByIDService,updateCartService, updateOrderService, cancelOrderService, orderStatusService, updateGofrugalOrderService } = require("../../services/EcommServices/OrderService");
const db = require('../../../config/connection');
const axios = require('axios');



const { requiredParams } = require('../../utility/requiredCheck');
const { getgofrugalkeys } = require("../../services/EcommServices/GofrugalService");
const { convertDateFormat } = require("../../utility/dateformat");

class OrderController {
  static async createOrder(req, res) {
    const payload = req.body;
    //console.log('createOrder payload', payload);
    const { user_id, orderDetails = [] } = payload;
    if (user_id == null || user_id == undefined) return requiredParams(res, 'user id is required!');
    if (orderDetails.length < 1) return requiredParams(res, 'order details item required');
    const [result] = await createOrderService(payload);
    //console.log('result', result);
    try {
      if (result) {
        // await getnotificationService({receiver_id :JSON.stringify(user_id), data:{title : "ORDER CREATED", body :"Your order has been created.", type : "order", "order_id":JSON.stringify(result.order_id) }});
        return res.status(200).send({
          success: true,
          msg: `${result.msg}`,
          data: {
            "order_id": result.order_id
          },
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {}
      });
    }
  }


  static async orders(req, res) {
    const param = req.params.id;
    if (param) {
      try {
        const payload = {
          'type': 'staff',
          'order_id': param
        }
        const result = await getOrderByIDService(payload);
        if (result) {
          return res.status(200).send({
            success: true,
            msg: 'Single order by id',
            data: result,
          });
        }
      } catch (error) {
        return res.status(200).send({
          success: false,
          msg: error.message || `Something went wrong`,
          data: []
        });
      }

    } else {
      //console.log('getOrders payload', payload);

      const storeid = req.params.storeid;

      const payload = {
        'type': 'package',
        'store_id': storeid

      }
      const [result, meta] = await getOrderService(payload);
      // console.log('result', result);
      try {
        if (result) {
          return res.status(200).send({
            success: true,
            msg: (result.length !== 0) ? 'Orders List' : 'No Orders',
            data: result,
            meta: meta[0] || { total: 0 }
          });
        }
      } catch (error) {
        return res.status(200).send({
          success: false,
          msg: error.message || `Something went wrong`,
          data: []
        });
      }
    }
  }


  static async getOrders(req, res) {
    const payload = req.body;
    //console.log('getOrders payload', payload);
    const { user_id, type } = payload;


    if (type == 'user') {
      if (user_id == null || user_id == undefined) return requiredParams(res, 'user id is required!');
    }
    const [result, meta] = await getOrderService(payload);
    console.log('result', result);
    try {
      if (result) {
        return res.status(200).send({
          success: true,
          msg: (result.length !== 0) ? 'Orders List' : 'No Orders',
          data: result,
          meta: meta[0] || { total: 0 }
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: []
      });
    }
  }

  static async updateCart(req, res){
    console.log()
 const payload = req.body;

 
    //console.log('getProducts payload', payload);
    try {
      const [result] = await updateCartService(payload);
      if (result) {
        return res.status(200).send({
          success: true,
          msg:`${result.msg}`,
          data: result,
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: []
      });
    }
  }

  static async getcart(req, res){
    console.log()
 const payload = req.body;
    //console.log('getProducts payload', payload);
    try {
      const [result] = await getcartService(payload);
      if (result) {
        return res.status(200).send({
          success: true,
          msg:`Cart found`,
          data: result,
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: []
      });
    }
  }




  static async getOrderByID(req, res) {
    const payload = req.body;
    //console.log('getProducts payload', payload);
    const { order_id } = payload;
    if (order_id == null || order_id == undefined) return requiredParams(res, 'Order id is required!');
    try {
      const result = await getOrderByIDService(payload);
      if (result) {
        return res.status(200).send({
          success: true,
          msg: 'Single order by id',
          data: result,
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: []
      });
    }
  }

  static async updateOrder(req, res) {
    const payload = req.body;
    //console.log('updateOrder payload', payload);
    const { order_id, status, grand_total, gst, sub_total, updated_by } = payload;
    if (order_id == null || order_id == undefined) return requiredParams(res, 'order_id is required!');
    if (status == null || status == undefined) return requiredParams(res, 'status is required!');
    if (grand_total == null || grand_total == undefined) return requiredParams(res, 'grand_total is required!');
    if (gst == null || gst == undefined) return requiredParams(res, 'gst is required!');
    if (sub_total == null || sub_total == undefined) return requiredParams(res, 'sub_total is required!');
    if (updated_by == null || updated_by == undefined) return requiredParams(res, 'updated_by is required!');
    try {
      const sql = `select user_id from orders where id = ?`;
      const [[ordered_by]] = await db.promise().query(sql, order_id);
      const [result] = await updateOrderService(payload);
      console.log('result', result);
      if (result) {

        const sql = `select user_id, store_id, status from orders where id = ?`;
        const [[order]] = await db.promise().query(sql, order_id);
        if (order.status == "order_unpaid") {
          await updateGofrugalOrderService({"order_id":order_id})

          const sql = `select events from webhook where brand_id =1`;

          const [[{events}]] = await db.promise().query(sql);
          
          if(events.includes("placed")){
          const webhookpayload = {
            "store_id": order.store_id,
            "order_status": order.status,

            "order_id": order_id
          };
          
        
          await CallWebhook(webhookpayload);
        }

        }
        // if(result.status == 1){
        //   await getnotificationService({receiver_id :ordered_by.user_id, data:{title : "ORDER UPDATED", body :"Your order has been updated.", type : "order", "order_id":JSON.stringify(order_id) }});
        // }
        return res.status(200).send({
          success: true,
          msg: `${result.msg}`,
          data: {},
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: []
      });
    }
  }

  static async cancelOrder(req, res) {
    const payload = req.body;
    //console.log('cancelOrder payload', payload);
    const { cancel_reason, cancelled_by, updated_by, order_id } = payload;
    if (cancel_reason == null || cancel_reason == undefined) return requiredParams(res, 'cancel_reason is required!');
    if (cancel_reason.length > 128) return requiredParams(res, 'cancel_reason text is too long!');
    if (cancelled_by == null || cancelled_by == undefined) return requiredParams(res, 'cancelled_by is required!');
    if (updated_by == null || updated_by == undefined) return requiredParams(res, 'updated_by is required!');
    if (order_id == null || order_id == undefined) return requiredParams(res, 'order_id is required!');
    const checkSQL = `select count(*) as status from orders where id = ${order_id}`;
    const [[isExist]] = await db.promise().query(checkSQL);
    console.log('isExist', isExist);
    try {
      if (isExist.status > 0) {
        // const checkSQL = `select count(*) as status from delivery where order_id = ?`;
        // const [[isExist]] = await db.promise().query(checkSQL, order_id); 
        // if(isExist.status > 0){
        //   const resultbysdf = await cancelDeliveryOrderService(payload);
        //   console.log('resultbysdf', resultbysdf);
        //   if (resultbysdf) {
        //     return res.status(200).send({
        //       success: resultbysdf.status,
        //       msg: resultbysdf.msg,
        //       data: resultbysdf.data
        //     });
        //   } else {
        //     return res.status(200).send({
        //       success: false,
        //       msg: 'Unable to cancel order, contact support with code 1001 !',
        //       data: {}
        //     });
        //   }
        // }else{
        const [result] = await cancelOrderService(payload);
        console.log('result', result);
        const sql = `select user_id, store_id, status from orders where id = ?`;
        const [[order]] = await db.promise().query(sql, order_id);
        if (result) {

          await getnotificationService({ receiver_id: order.user_id, data: { title: "ORDER CANCELLED", body: "Your order is cancelled.", type: "order", "order_id": JSON.stringify(order_id) } });
          await updateGofrugalOrderService({"order_id":order_id,"mode":"cancel"})

        
          const sql = `select events from webhook where brand_id =1`;
          const [[{events}]] = await db.promise().query(sql);
          if(events.includes("cancelled")){
          const webhookpayload = {
            "store_id": order.store_id,
            "order_status": "cancelled",
            "order_id": order_id
          };
        
      const result=  await CallWebhook(webhookpayload);
      console.log(result);
        }

          return res.status(200).send({
            success: true,
            msg: 'Order Cancelled Successfully',
            data: {}
          });
        }
      } else {
        return res.status(200).send({
          success: false,
          msg: 'Order does not exist or Unable to cancel order',
          data: {}
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: []
      });
    }
  }

  static async Orderstatus(req, res) {
    const payload = req.body;
    const { updated_by, order_id } = payload;
    if (order_id == null || order_id == undefined) return requiredParams(res, 'order_id is required!');
    if (updated_by == null || updated_by == undefined) return requiredParams(res, 'updated_by is required!');
    try {
      const [result] = await orderStatusService(payload);
      console.log('result', result);
      const sql = `select user_id from orders where id = ?`;
      const [[ordered_by]] = await db.promise().query(sql, order_id);
      if (result) {
        await getnotificationService({ receiver_id: ordered_by.user_id, data: { title: "ORDER PAID", body: "Your order is paid", type: "order", "order_id": JSON.stringify(order_id) } });
        return res.status(200).send({
          success: true,
          msg: `${result.msg}`,
          data: {},
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: []
      });
    }
  }

  static async getGofrugalOrders(req,res){
    const payload = req.body;
    try {
      const [[{domainpath,token}]] = await getgofrugalkeys();
      const {start_date,end_date,page,size,store_id,filter,sort } = payload;
      let customFilter = "";
      if (start_date && end_date) {
        customFilter = `?q=createdAt>=${convertDateFormat(start_date,"start")},createdAt<=${convertDateFormat(end_date,"end")}`;
      }
      if (filter) {
        customFilter += customFilter ? `,isOfflineOrder==0` : `?q=isOfflineOrder==0`;
      }
      else{
        customFilter += customFilter ? `,isOfflineOrder==1` : `?q=isOfflineOrder==1`;

      }
      if (store_id) {
        customFilter += customFilter ? `,outletId==${store_id}` : `?q=outletId==${store_id}`;
      }
      if (page) {
        customFilter += customFilter ? `&page=${page+1}` : `?page=${page+1}`;
      }
      if (size) {
        customFilter += customFilter ? `&limit=${size}` : `?limit=${size}`;
      }
      if ( sort?.order_by === "asc") {
        customFilter += customFilter ? `&order=${sort?.field}` : `?order=${sort?.field}`;
      }
      const options = {
        'method': 'GET',
        'url': `${domainpath}/api/v1/salesOrders${customFilter}`,
        'headers': {
          'Content-Type': 'application/json',
          'X-Auth-Token':token
        },
      };
      const result = await axios(options);
      if (result) {
        return res.status(200).send({
          success: true,
          msg: !result.data.salesOrders?"No orders":"Gofrugal Orders",
          data: !result.data.salesOrders?[]:result.data.salesOrders,
          meta:{
            total:result.data.total_records
          }
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: []
      });
    }
  }


  


  

}


module.exports = OrderController;
