const { createReviewService, updateReviewService, getReviewService, setOrderReviewService } = require("../../services/EcommServices/ReviewService");
const { requiredParams } = require('../../utility/requiredCheck');
class ReviewController {
    static async createReview(req, res) {
        const payload = req.body;
        const { user_id, rating, product_id, order_id } = payload;
        if (user_id == null || user_id == undefined) return requiredParams(res, 'user_id is required!');
        if (rating == null || rating == undefined) return requiredParams(res, 'rating is required!');
        if (product_id == null || product_id == undefined) return requiredParams(res, 'product_id is required!');
        if (order_id == null || order_id == undefined) return requiredParams(res, 'order_id is required!');
        if (!Number(product_id) && !Number(user_id) && !Number(order_id)) return requiredParams(res, 'Please input valid numeric value!');
        const [result] = await createReviewService(payload);
        //console.log('result', result);
        try {
            if (result) {
                return res.status(200).send({
                    success: true,
                    msg: `${result.msg}`,
                    data: []
                });
            }
        } catch (error) {
            return res.status(200).send({
                success: false,
                msg: error.message || `Something went wrong`,
                data: []
            });
        }
    }

    static async updateReview(req, res) {
        const payload = req.body;
        const { review_id, rating, description } = payload;
        if (review_id == null || payload.review_id == undefined) return requiredParams(res, 'review_id is required!');
        if (rating == null || rating == undefined) return requiredParams(res, 'rating is required!');
        if (description == null || description == undefined) return requiredParams(res, 'description is required!');
        if (!Number(review_id)) return requiredParams(res, 'Please input valid numeric value!');
        //console.log('updateBrandService payload', payload);
        const [result] = await updateReviewService(payload);
        //console.log('result', result);
        try {
            if (result) {
                return res.status(200).send({
                    success: true,
                    msg: `${result.msg}`,
                    data: []
                });
            }
        } catch (error) {
            return res.status(200).send({
                success: false,
                msg: error.message || `Something went wrong`,
                data: []
            });
        }
    }
    static async getReview(req, res) {
        const payload = req.body;
        //console.log('getReviewService payload', payload);
        const [result, meta] = await getReviewService(payload);
        //console.log('result', result);
        try {
            if (result) {
                return res.status(200).send({
                    success: true,
                    msg: (result.length !== 0) ? 'Review List' : 'No Reviews',
                    data: result,
                    meta: meta[0] || { total: 0 }
                });
            }
        } catch (error) {
            return res.status(200).send({
                success: false,
                msg: error.message || `Something went wrong`,
                data: []
            });
        }
    }
    static async setOrderReview(req, res) {
        const payload = req.body;
        const { order_rating, order_id } = payload;
        if (order_rating == null || order_rating == undefined) return requiredParams(res, 'order_rating is required!');
        if (order_id == null || order_id == undefined) return requiredParams(res, 'order_id is required!');
        if (!Number(order_rating) && !Number(order_id)) return requiredParams(res, 'Please input valid numeric value!');
        const [result] = await setOrderReviewService(payload);
        try {
            if (result) {
                return res.status(200).send({
                    success: true,
                    msg: `${result.msg}`,
                    data: []
                });
            }
        } catch (error) {
            return res.status(200).send({
                success: false,
                msg: error.message || `Something went wrong`,
                data: []
            });
        }
    }
}

module.exports = ReviewController;