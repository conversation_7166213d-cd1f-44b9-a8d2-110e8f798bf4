const db = require('../../config/connection');

const updateConfigService = async (payload) => {
    try {
        const procedure = `CALL spUpdateStockConfig(?)`;
        const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
        if (row) {
            return row;
        } else {
            return null;
        }
    } catch (error) {
        throw new Error(error);
    }
}

const getConfigService = async (payload) => {
    try {
        const procedure = `CALL spGetStockConfig(?)`;
        const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
        if (row) {
            return row;
        } else {
            return null;
        }
    } catch (error) {
        throw new Error(error);
    }
}

module.exports = { updateConfigService,getConfigService }