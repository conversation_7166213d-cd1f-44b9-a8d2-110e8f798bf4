const db = require('../../../config/connection');

const totalUsersService = async (payload) => {
    const { type } = payload;
    try {
        let where = '';
        if (type == 'month') {
            where = `DATE(u.create_at) >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)  and DATE(u.create_at) <= CURDATE()`;
        } else {
            where = `DATE(u.create_at) >= DATE_SUB(CURDATE(), INTERVAL 6 DAY)  and DATE(u.create_at) <= CURDATE()`;
        }
        const sql = `select count(u.id) as total_users from users u
        where u.role_id = 3 and ${where}`;
        const [[users]] = await db.promise().query(sql);

        const weekly = `SELECT
        (DATE(NOW()) - INTERVAL day DAY) AS DayDate,
        COUNT(id) AS count
        FROM (
            SELECT 0 AS day
            UNION SELECT 1
            UNION SELECT 2
            UNION SELECT 3
            UNION SELECT 4
            UNION SELECT 5
            UNION SELECT 6
        ) AS week
        LEFT JOIN users u ON DATE(u.create_at) = (DATE(NOW()) - INTERVAL day DAY)
        GROUP BY DayDate
        ORDER BY DayDate ASC;`;

        const monthly = `CALL spGetOrdersChart(?)`;
        let row = ``;
        let result = [];
        if (type == "month") {
            [[row]] = await db.promise().query(monthly, JSON.stringify(payload));
            console.log('row', row);
            for (let value of Object.values(row)) {
                console.log('value', value.count);
                result.push(value.count);
            }
        } else {
            row = await db.promise().query(weekly);
            console.log('row', row[0]);
            for (let value of Object.values(row[0])) {
                console.log('value', value.count);
                result.push(value.count);
            }
        }
        console.log('result', result);
        const usersobj = { linegraph: result || [], total_users: users.total_users || 0 }
        if (result) {
            return usersobj;
        } else {
            return null;
        }
    } catch (error) {
        throw new Error(error);
    }
}

const dashboardCountsService = async (payload) => {
    console.log('payload', payload);
    try {
        let where = '';
        let revWhere = '';
        let prodWhere = '';
        if(payload.store_id == null || payload.store_id == undefined ){
            where
           }else{
            where += `where o.store_id = ${payload.store_id}`;
            revWhere += `and o.store_id = ${payload.store_id}`;
            prodWhere += `left join storewise_variants sv on sv.ref_id= pv.ref_id and sv.store_id = ${payload.store_id}
            where sv.is_active  = 1`;
           }
        const sql = `select count(u.id) as total_users from users u
        where u.role_id = 3`;
        const [[users]] = await db.promise().query(sql);
        const sqlOrder = `select count(o.id) as total_orders  from orders o ${where}`;
        const [[orders]] = await db.promise().query(sqlOrder);
        const sqlrevenue = `select  sum(o.grand_total) as total_revenue  from orders o
                           where o.status = "order_paid" ${revWhere}`;
        const [[revenue]] = await db.promise().query(sqlrevenue); 
        const sqlProduct = `select count(pv.id) as total_products from product_variant pv ${prodWhere}`;
        const [[products]] = await db.promise().query(sqlProduct);
        const topprod = `CALL spGetTopProduct(?)`;
        const [[topProduct]] = await db.promise().query(topprod, JSON.stringify(payload));
        console.log('topProduct', topProduct);
        const row = { users, orders, revenue, products, topProduct: (topProduct.length) > 0 ? topProduct : {} };
        console.log('row', row);
        if (orders) {
            return row;
        } else {
            return null;
        }
    } catch (error) {
        throw new Error(error);
    }
}

const OrdersChartService = async (payload) => {
    try {
        let where = `where o.status IN("order_created","order_paid","order_cancelled")`;
        if(payload.store_id == null || payload.store_id == undefined ){
         where
        }else{
         where += `and o.store_id = ${payload.store_id}`
        }
        const sql = `select o.status as type,COUNT(o.id) as value from orders o
                     ${where}
                     group by status
                     ORDER BY FIELD(o.status, "order_created", "order_paid", "order_cancelled")`;
        const pierow = await db.promise().query(sql);
        const procedure = `CALL spGetHourlyOrders(?)`;
        const [area] = await db.promise().query(procedure, JSON.stringify(payload));
        const newArray = area[0].map((name, i) => {
            const r = Object.keys(name).map((m, i) => {
                return { time: m }
            });
            const s = Object.values(name).map((m, i) => {
                return { orders: m }
            });
            const final = r.map((item, i) => Object.assign({}, item, s[i]));
            return final;
        });
        console.log('newArray', newArray);
        console.log('pierow', pierow[0]);
        let final = [];
        const allstatus = ["order_created", "order_paid", "order_cancelled"];
        allstatus.map(m => {
            pierow[0].map(s => {
                if (m == s.type) {
                    final.push({
                        "type": m,
                        "value": s.value || 0
                    });
                }
                return s.type
            });
        });
        const mapRow = pierow[0].map(s => { return s.type });
        const filter = allstatus.filter(item => {
            return !mapRow.includes(item)
        });
        if (filter.length != 0) {
            filter.map(s => {
                final.push({
                    "type": s,
                    "value": 0
                });
            });
        }
        console.log('filter', filter);

        const row = { piechart: final || [], hourly_chart: newArray[0] || [] };
        console.log('row', row);
        if (row) {
            return row;
        } else {
            return null;
        }
    } catch (error) {
        throw new Error(error);
    }
}


const registerWebhookService = async (payload) => {
    try {
        const procedure = `call spregisterWebhook(?)`;
        const [row] = await db.promise().query(procedure, JSON.stringify(payload));
        if (row) {
            return row;
        } else {
            return null;
        }
    } catch (error) {
        throw new Error(error);
    }
}
const getWebhookService = async (payload) => {
    try {
        const procedure = `select id,webhookurl,events,CAST(is_active AS SIGNED) as is_active, updated_at  from webhook where brand_id=1`;
        const [row] = await db.promise().query(procedure);
        if (row) {
            return row;
        } else {
            return null;
        }
    } catch (error) {
        throw new Error(error);
    }
}



const getStoresService = async (keyword) => {
    try {
        const procedure = `call spGetStores(?)`;
        const [row] = await db.promise().query(procedure, JSON.stringify(keyword));
        if (row) {
            return row;
        } else {
            return null;
        }
    } catch (error) {
        throw new Error(error);
    }
}

module.exports = { totalUsersService, OrdersChartService, dashboardCountsService,getStoresService, registerWebhookService,getWebhookService }