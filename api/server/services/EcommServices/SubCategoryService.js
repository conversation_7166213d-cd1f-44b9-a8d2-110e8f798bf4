
const db = require('../../../config/connection');

const createSubCategoryService = async (payload) => {
    try {
        const procedure = `CALL spCreateSubCategory(?)`;
        const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
        if (row) {
            return row;
        } else {
            return null;
        }
    } catch (error) {
        throw new Error(error);
    }
}

const updateSubCategoryService = async (payload) => {
    try {
        const procedure = `CALL spUpdateSubCategory(?)`;
        const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
        if (row) {
            return row;
        } else {
            return null;
        }
    } catch (error) {
        throw new Error(error);
    }
}

const getSubCategoryService = async (keyword) => {
    try {
        const procedure = `call spGetSubCategory(?)`;
        const [row] = await db.promise().query(procedure, JSON.stringify(keyword));
        if (row) {
            return row;
        } else {
            return null;
        }
    } catch (error) {
        throw new Error(error);
    }
}

module.exports = { createSubCategoryService, updateSubCategoryService, getSubCategoryService }