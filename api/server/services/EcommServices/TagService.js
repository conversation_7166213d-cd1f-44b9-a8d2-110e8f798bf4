const db = require('../../../config/connection');

const createTagService = async (payload) => {
    try {
        const procedure = `CALL spCreateTag(?)`;
        const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
        if (row) {
            return row;
        } else {
            return null;
        }
    } catch (error) {
        throw new Error(error);
    }
}

const updateTagService = async (payload) => {
    try {
        const procedure = `CALL spUpdateTag(?)`;
        const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
        if (row) {
            return row;
        } else {
            return null;
        }
    } catch (error) {
        throw new Error(error);
    }
}

const getTagService = async (keyword) => {
    try {
        const procedure = `call spGetTag(?)`;
        const [row] = await db.promise().query(procedure, JSON.stringify(keyword));
        if (row) {
            return row;
        } else {
            return null;
        }
    } catch (error) {
        throw new Error(error);
    }
}

module.exports = { createTagService, updateTagService, getTagService }