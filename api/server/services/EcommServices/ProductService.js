const db = require('../../../config/connection');

const getProductsMayLikes = async (payload) => {
  try {
    const procedure = `CALL spGetProductsMayLikes(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

const getFiveProductsPerCategoryService = async (payload) => {
  try {
    const procedure = `CALL spGetFiveProductsPerCategory(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));

    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw error;
  }
};

const getProductService = async (payload) => {
  try {
    const procedure = `CALL spGetProducts(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

const getProductByIDService = async (payload) => {
  try {
    const procedure = `CALL spGetProductByID(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    console.log("row", row)
    if (row) {
      return row;
    } else {
      return null;
    }
    //return second[0].total > 0 ? row[0] : {};
  } catch (error) {
    throw new Error(error);
  }
}

const getProductsByCategoryService = async (payload) => {
  try {
    const procedure = `CALL spGetProductsByCategory(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

const filterProductsService = async (payload) => {
  try {
    const procedure = `CALL spFilterProducts(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw error;
  }
}

const createProductService = async (payload) => {
  try {
    const procedure = `CALL spCreateProduct(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

const updateProductService = async (payload) => {
  try {
    const procedure = `CALL spUpdateProduct(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

const createVariantService = async (payload) => {
  try {
    const procedure = `CALL spCreateVariant(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

const updateVariantService = async (payload) => {
  try {
    const procedure = `CALL spUpdateVariant(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

const getVariantsService = async (payload) => {
  try {
    const procedure = `CALL spGetVariant(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    console.log('row',row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

const popularProductsService = async (payload) => {
  try {
    const procedure = `CALL spPopularProducts(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

const validateDiscountService = async (payload) => {
  try {
    const procedure = `CALL spValidateCoupon(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

const uploadImageService = async (fileName) => {
  try {
    const procedure = `CALL spUploadImage(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(fileName));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

const deleteImageService = async (fileName) => {
  try {
    const procedure = `CALL spDeleteImage(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(fileName));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

const newInStockService = async (payload) => {
  try {
    const procedure = `CALL spGetNewStock(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

const uploadInventoryService = async (fileName) => {
  try {
    const procedure = `CALL spuploadInventory(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(fileName));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

const uploadStoreService = async (fileName) => {
  try {
    const procedure = `CALL spuploadStore(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(fileName));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

module.exports = { getProductsMayLikes, getProductService, getProductByIDService, getProductsByCategoryService, createProductService, updateProductService, popularProductsService, validateDiscountService, uploadImageService, deleteImageService, newInStockService, updateVariantService, createVariantService, getVariantsService, uploadInventoryService, uploadStoreService, getFiveProductsPerCategoryService, filterProductsService };
