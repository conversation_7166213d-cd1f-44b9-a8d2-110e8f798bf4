
const db = require('../../../config/connection');

const getSubscriptionService = async (payload) => {
    try {
        const procedure = `CALL spGetSubscriptionList(?)`;
        const [row] = await db.promise().query(procedure, JSON.stringify(payload));
        if (row) {
          return row;
        } else {
          return null;
        }
      } catch (error) {
        throw new Error(error);
      }
    }
    
module.exports = { getSubscriptionService }
