
const { default: axios } = require('axios');
const db = require('../../../config/connection');

const placeDeliveryOrderService = async (payload) => {

  const { store_code, drop_instruction_text, order_id } = payload;

  const [[clientIntegration]] = await db.promise().query(`select * from client_integration where type = 'delivery' and is_active =1`);

  const procedure = `CALL spPlaceDeliveryOrder(?)`;
  const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));

  console.log('row res', row);
  console.log('product_details', row[0].product_details);
  const minutesToAdd = 10;
  const currentDate = new Date();
  const scheduled_time = new Date(currentDate.getTime() + minutesToAdd * 60000);

  const reqPayload = {
    "pickup_contact_number": row[0].pickup_contact_number || '',
    "store_code": store_code,
    "order_details": {
      "scheduled_time": scheduled_time,
      "order_value": row[0].grand_total,
      "paid": false,
      "client_order_id": row[0].order_id,
      "delivery_instruction": {
        "drop_instruction_text": drop_instruction_text || '',
        "take_drop_off_picture": true,
        "drop_off_picture_mandatory": true
      }
    },
    "customer_details": row[0].customer_details,
    "misc": {
      "type": "express",
      "pickup_otp": row[0].pickup_otp,
      "return_otp": row[0].return_otp,
      // "promised_delivery_time": "2019-04-16 12:00:00",
      // "weight": 15.0
    },
    "product_details": row[0].product_details
  };

  try {
    const options = {
      'method': 'POST',
      'url': `${clientIntegration.domain}/api/v2/stores/orders/`,
      'headers': {
        'Authorization': clientIntegration.secret,
        'Content-Type': 'application/json'
      },
      data: JSON.stringify(reqPayload)
    };
    const orderResult = await axios(options);
    console.log('orderResult', orderResult.status);
    console.log('orderResult', orderResult.data);

    if (orderResult.status == 201) {
      const deliverySP = `CALL spCreateDeliveryOrder(?)`;
      const [[delResult]] = await db.promise().query(deliverySP, JSON.stringify(orderResult.data.data));
      console.log('sp:::', delResult);
      if (delResult) {
        return { status: delResult[0].status, track_url: orderResult.data.data.track_url, sfx_order_id: orderResult.data.data.sfx_order_id, order_id: order_id };
      } else {
        return null;
      }
    } else {
      return 'Invalid order id';
    }

  } catch (error) {
    // return 'invalid order id';
    return null
  }
}



const cancelDeliveryOrderService = async (payload) => {
  const { cancel_reason, cancelled_by, order_id } = payload;
  try {
    const [[clientIntegration]] = await db.promise().query(`select * from client_integration where type = 'delivery' and is_active =1`);
    const reqPayload = {
      "reason": cancel_reason,
      "user": cancelled_by
    }
    const options = {
      'method': 'PUT',
      'url': `${clientIntegration.domain}/api/v2/orders/${order_id}/cancel`,
      'headers': {
        'Authorization': clientIntegration.secret,
        'Content-Type': 'application/json'
      },
      data: JSON.stringify(reqPayload)
    };
    const cancleResult = await axios(options);

    console.log('cancleResult', cancleResult);

    if (cancleResult.status === 200) {
      return { status: true, msg: 'Order Cancelled Successfully', data: cancleResult.data.data };
    } else {
      return { status: false, msg: 'Unable to cancle order', data: {} };
    }
  } catch (error) {
    throw new Error(error);
  }

}
const callbackDeliveryStatusService = async (payload) => {
  try {

    const procedure = `CALL spOrderStatusCallback(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));

    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }

}


module.exports = { placeDeliveryOrderService, cancelDeliveryOrderService, callbackDeliveryStatusService }