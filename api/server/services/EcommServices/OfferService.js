const db = require('../../../config/connection');

const createOfferService = async (payload) => {
    try {
        const procedure = `CALL spCreateOffer(?)`;
        const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
        if (row) {
            return row;
        } else {
            return null;
        }
    } catch (error) {
        throw new Error(error);
    }
}

const updateOfferService = async (payload) => {
    try {
        const procedure = `CALL spUpdateOffer(?)`;
        const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
        if (row) {
            return row;
        } else {
            return null;
        }
    } catch (error) {
        throw new Error(error);
    }
}

const getOfferService = async (keyword) => {
    try {
        const procedure = `call spGetOffers(?)`;
        const [row] = await db.promise().query(procedure, JSON.stringify(keyword));
        if (row) {
            return row;
        } else {
            return null;
        }
    } catch (error) {
        throw new Error(error);
    }
}

const getHomePageOfferService = async (keyword) => {
    try {
        const procedure = `call spGetHomePageOffers(?)`;
        const [row] = await db.promise().query(procedure, JSON.stringify(keyword));
        if (row) {
            return row;
        } else {
            return null;
        }
    } catch (error) {
        throw new Error(error);
    }
}

const createBannerService = async (payload) => {
    try {
        const procedure = `CALL spCreateBanner(?)`;
        const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
        if (row) {
            return row;
        } else {
            return null;
        }
    } catch (error) {
        throw new Error(error);
    }
}

const updateBannerService = async (payload) => {
    try {
        const procedure = `CALL spUpdateBanner(?)`;
        const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
        if (row) {
            return row;
        } else {
            return null;
        }
    } catch (error) {
        throw new Error(error);
    }
}

const getBannerService = async (payload) => {
    try {
        const procedure = `CALL spGetBanner(?)`;
        const [row] = await db.promise().query(procedure, JSON.stringify(payload));
        console.log('row',row);
        if (row) {
            return row;
        } else {
            return null;
        }
    } catch (error) {
        throw new Error(error);
    }
}

module.exports = { createOfferService, updateOfferService, getOfferService, getHomePageOfferService, createBannerService, getBannerService, updateBannerService }