
const db = require('../../../config/connection');

const exportCsvReportService = async (payload) => {
  try {
    const { order_id, type, start_date, end_date } = payload;
    let csvquery = `
    SELECT
			o.id AS order_id,
			o.created_at, o.sub_total as total, o.gst, o.grand_total, o.discount,
			CONCAT(IFNULL(u.first_name, ''), ' ', IFNULL(u.last_name, '')) AS 'full_name',
			u.mobile as customer_number, u.email,
			(SELECT  name FROM store s WHERE s.id = o.store_id) AS store_name,
			o.status, o.grand_total,
			CONCAT(ua.name, ua.address_type, ' - ', ua.address1, ' ', ua.address2, ' ', ua.city, ' ', ua.pincode, ' ', ua.state)
			AS address, ua.pincode,
			GROUP_CONCAT(CONCAT('(', pv.name, ' x ', od.quantity, ')') SEPARATOR ',') AS list,
			SUM(od.quantity * pv.weight) AS weight
			FROM orders o
				LEFT JOIN
			order_details od ON o.id = od.order_id
				LEFT JOIN
			product_variant pv ON od.variant_id = pv.id
			LEFT JOIN
		user_address ua ON o.address_id = ua.id
			LEFT JOIN
		users u ON o.user_id = u.id
    where 1=1
    `;
    // WHERE o.id IN (61,62,63)
    // GROUP BY o.id

    const params = [];
    if (type == 'id') {
      csvquery += ' and o.id in (?) GROUP BY o.id';
      params.push(order_id);
    }
    if (type == 'date') {
      csvquery += 'and o.created_at between ? and ? GROUP BY o.id';
      params.push(start_date, end_date);
    }

    const row = await db.promise().query(csvquery, params);
    // console.log('row', row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

const exportVaraintCsvService = async (payload) => {
  try {
    const { variant_id, type, start_date, end_date, store_id } = payload;
    let csvquery = '';
    if(store_id == null || store_id == undefined){
      csvquery = `select c.name as category_name,sc.name as sub_category_name,scg.name as sub_category_group_name,p.name as product_name,p.description,pv.name as variant_name,pv.weight,pv.sku,pv.ref_id
    from product_variant pv
      left join products p on p.id = pv.product_id 
      left join sub_category sc on sc.id = p.sub_category_id 
      left join category c on c.id = p.category_id 
      left join sub_category_group scg on scg.id = p.sub_category_group_id
      where 1=1`
    }else{
      csvquery = `select c.name as category_name,sc.name as sub_category_name,scg.name as sub_category_group_name,p.name as product_name,p.description,pv.name as variant_name,pv.weight,pv.sku,sv.cost_price, sv.mrp ,sv.price,sv.stock,sv.is_active as store_is_active,pv.ref_id,sv.store_id
    from product_variant pv
      left join products p on p.id = pv.product_id 
      left join sub_category sc on sc.id = p.sub_category_id 
      left join category c on c.id = p.category_id 
      left join sub_category_group scg on scg.id = p.sub_category_group_id
      left join storewise_variants sv on sv.ref_id = pv.ref_id and sv.store_id = ${store_id}
      where 1=1`
    }

    const params = [];
    if (type == 'id') {
      csvquery += ' and pv.id in (?) ORDER by CAST(SUBSTRING_INDEX(SUBSTR(pv.ref_id ,5),"_",1) AS UNSIGNED) desc,CAST(SUBSTRING_INDEX(SUBSTR(pv.ref_id ,5),"_",-1) AS UNSIGNED) desc';
      params.push(variant_id);
    } else if (type == 'date') {
      csvquery += ' and (DATE(pv.created_at) >= ? and DATE(pv.created_at) <= ?) ORDER by CAST(SUBSTRING_INDEX(SUBSTR(pv.ref_id ,5),"_",1) AS UNSIGNED) desc,CAST(SUBSTRING_INDEX(SUBSTR(pv.ref_id ,5),"_",-1) AS UNSIGNED) desc';
      params.push(start_date, end_date);
    } else {
      csvquery += ' ORDER by CAST(SUBSTRING_INDEX(SUBSTR(pv.ref_id ,5),"_",1) AS UNSIGNED) desc,CAST(SUBSTRING_INDEX(SUBSTR(pv.ref_id ,5),"_",-1) AS UNSIGNED) desc';
    }

    const row = await db.promise().query(csvquery, params);
    // const procedure = `CALL spExportVariantCSV(?)`;
    // const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    // console.log('row', row[0]);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

module.exports = { exportCsvReportService, exportVaraintCsvService };