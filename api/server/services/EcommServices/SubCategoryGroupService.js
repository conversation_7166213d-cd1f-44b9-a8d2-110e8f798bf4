
const db = require('../../../config/connection');

const createSubCategoryGroupService = async (payload) => {
    try {
        const procedure = `CALL spCreateSubCategoryGroup(?)`;
        const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
        if (row) {
            return row;
        } else {
            return null;
        }
    } catch (error) {
        throw new Error(error);
    }
}

const updateSubCategoryGroupService = async (payload) => {
    try {
        const procedure = `CALL spUpdateSubCategoryGroup(?)`;
        const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
        if (row) {
            return row;
        } else {
            return null;
        }
    } catch (error) {
        throw new Error(error);
    }
}

const getSubCategoryGroupService = async (keyword) => {
    try {
        const procedure = `call spGetSubCategoryGroup(?)`;
        const [row] = await db.promise().query(procedure, JSON.stringify(keyword));
        if (row) {
            return row;
        } else {
            return null;
        }
    } catch (error) {
        throw new Error(error);
    }
}

module.exports = { createSubCategoryGroupService, updateSubCategoryGroupService, getSubCategoryGroupService }