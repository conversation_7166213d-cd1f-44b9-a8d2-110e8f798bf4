const db = require('../../../config/connection');

const createReviewService = async (payload) => {
    try {
        const procedure = `CALL spCreateReview(?)`;
        const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
        if (row) {
            return row;
        } else {
            return null;
        }
    } catch (error) {
        throw new Error(error);
    }
}
const updateReviewService = async (payload) => {
    try {
        const procedure = `CALL spUpdateReview(?)`;
        const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
        if (row) {
            return row;
        } else {
            return null;
        }
    } catch (error) {
        throw new Error(error);
    }
}
const getReviewService = async (payload) => {
    try {
        const procedure = `CALL spGetReview(?)`;
        const [row] = await db.promise().query(procedure, JSON.stringify(payload));
        if (row) {
            return row;
        } else {
            return null;
        }
    } catch (error) {
        throw new Error(error);
    }
}
const setOrderReviewService = async (payload) => {
    try {
        const procedure = `CALL spSetOrderReview(?)`;
        const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
        if (row) {
            return row;
        } else {
            return null;
        }
    } catch (error) {
        throw error;
    }
}


module.exports = { createReviewService, updateReviewService, getReviewService, setOrderReviewService }