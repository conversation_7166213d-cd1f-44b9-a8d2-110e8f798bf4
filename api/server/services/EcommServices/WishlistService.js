const db = require('../../../config/connection');

const updateWishlistService = async (payload) => {
  try {
    const procedure = `CALL spCreateDeleteWishList(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}
const getWishlistService = async (payload) => {
  try {
    const procedure = `CALL spGetWishList(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

module.exports = { updateWishlistService, getWishlistService };