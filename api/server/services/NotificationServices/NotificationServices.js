const db = require('../../../config/connection');
const { admin } = require('../../../config/firebase-config');
const axios = require('axios');


const getnotificationService = async (payload) => {
  const { receiver_id, data } = payload;
  console.log("payload", payload);
  try {
    const sql = `select firebase_id as fire_base_id from users where id = ?`;
    const [[row]] = await db.promise().query(sql, receiver_id);
    if (row) {
      console.log("row", row.fire_base_id);
      const message = {
        data: data,
        apns: {
          headers: {
            "apns-priority": "10"
          },
          payload: {
            aps: {
              badge: 0,
              sound: "default",
              alert: {
                title: data.title,
                body: data.body,
                type: data.type,
              },
            },
            order_id: data.order_id,
          },
          fcm_options: {
            image: ''}
        },
        token: row.fire_base_id,
      };
      const msg = await admin.messaging().send(message);
          if (msg) {
            return msg
          } else {
            return null;
          }
    }
  } catch (error) {
    throw new Error(error);
  }
}

const getDynamicLink = async (payload) => {
  const suffix = payload;
  console.log('payload in notification', payload);
  try {
const 
payloadData={
"longDynamicLink": `https://rapsap.page.link/?link=https://rapsap.com/${suffix}&apn=consumer.rapsap.com&isi=6443778088&ibi=com.rapsap.app`,
"suffix": {
  "option": "SHORT"
}
}
      const options = {
        'method': 'POST',
        'url': `https://firebasedynamiclinks.googleapis.com/v1/shortLinks?key=${process.env.WEB_API_KEY}`,
        'headers': {
          
          'Content-Type': 'application/json'
        },
        data: payloadData
      };

      const result = await axios(options);
      console.log('result ', result);
      // console.log('result', result.data);



      // if (result.data.status === "ACCEPTED") {
        return result.data
      // } 
      // else {
      //   return null
      // }
    } catch (error) {
      console.log('error', error);
    }
  }



const getWhatsappService = async (payload) => {
  const { user_id, data, type, template_name, body_values } = payload;
  console.log('payload in notification', payload);
  try {
      const temp = `select first_name as user_name, mobile,(select b.is_prod from brand b where b.id = 1) as is_prod from temp_user where id = ?`;
      const user = `select first_name as user_name, mobile,(select b.is_prod from brand b where b.id = 1) as is_prod from users where id = ?`;
      const sql = (type == "temp" ? temp : user);
      const [[row]] = await db.promise().query(sql, user_id);
      const name = data.name != null ? data.name : row.user_name;
      let payloadData = {};
      if (row.is_prod == 0) {
        payloadData = {
          "channelId": process.env.RAPSAP_GALLABOX_CHANNEL_ID,
          "channelType": "whatsapp",
          "recipient": {
            "name": name || "Rapsap User",
            "phone": data.mobile != null ? `91${data.mobile}` : `91${row.mobile}`
          },
          "whatsapp": {
            "type": "text",
            "text": {
              "body": data.message
            }
          }
        };
      } else {
        payloadData = {
          "channelId": process.env.RAPSAP_GALLABOX_CHANNEL_ID,
          "channelType": "whatsapp",
          "recipient": {
            "name": name || "Rapsap User",
            "phone": data.mobile != null ? `91${data.mobile}` : `91${row.mobile}`
          },
          "whatsapp": {
            "type": "template",
            "template": {
              "templateName": template_name,
              "bodyValues": body_values
            }
          }
        };
      }
      console.log('payloadData', payloadData);
      const options = {
        'method': 'POST',
        'url': `https://server.gallabox.com/devapi/messages/whatsapp`,
        'headers': {
          'apiSecret': process.env.RAPSAP_GALLABOX_API_SECRET,
          'apiKey': process.env.RAPSAP_GALLABOX_API_KEY,
          'Content-Type': 'application/json'
        },
        data: JSON.stringify(payloadData)
      };

      const result = await axios(options);
      console.log('result', result.data);

      if (result.data.status === "ACCEPTED") {
        return { status: true, msg: result.data.message, data: {} };
      } else {
        return null
      }
    } catch (error) {
      console.log('error', error);
    }
  }

  const CallWebhook = async (payload) => {
    const sql = `select webhookurl from webhook where brand_id =1`;

    const [[{webhookurl}]] = await db.promise().query(sql);
    const options = {
      'method': 'POST',
      'url': webhookurl,
      'headers': {
        'Content-Type': 'application/json'
      },
      data: JSON.stringify(payload)
    };
    return await axios(options);
  }


module.exports = { getnotificationService, getWhatsappService, CallWebhook, getDynamicLink };