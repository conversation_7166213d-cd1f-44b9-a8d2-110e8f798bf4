const db = require('../../../config/connection');

const loginWithGoogleIdTokenService = async (payload) => {
  try {
    const procedure = 'CALL spLoginWithGoogleIdToken(?)'
    const [[[row]]] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw error
  }
}

const sendOtpService = async (payload) => {
  try {
    const procedure = `CALL spSendOtp(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
}

const verifyOtpService = async (payload) => {
  try {
    const procedure = `CALL spVerifyOtp(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
}

const getUserDetailService = async (payload) => {
  try {
    const procedure = `CALL spGetUserById(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

const editProfileService = async (payload) => {
  try {
      const procedure = `CALL spEditProfile(?)`;
      const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
      if (row) {
          return row;
      } else {
          return null;
      }
  } catch (error) {
      throw new Error(error);
  }
}

const getOfferByUserIDService = async (payload) => {
  try {
    const procedure = `CALL spDailyOfferByUserID(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}
const getUserRewardsService = async (payload) => {
  try {
    // const procedure = `CALL spUserCouponOffer(?)`;
    const procedure = `CALL spGetUserRewards(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}
const openOfferByUserService = async (payload) => {
  try {
    const procedure = `CALL spOpenOfferByUser(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}
const claimOfferByUserService = async (payload) => {
  try {
    const procedure = `CALL spClaimOfferByUser(?)`;
    // const procedure = `CALL spGetUserRewards(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

const createAddressService = async (payload) => {
  try {
      const procedure = `CALL spCreateAddress(?)`;
      const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
      if (row) {
          return row;
      } else {
          return null;
      }
  } catch (error) {
      throw new Error(error);
  }
}

const updateAddressService = async (payload) => {
  try {
      const procedure = `CALL spUpdateAddress(?)`;
      const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
      if (row) {
          return row;
      } else {
          return null;
      }
  } catch (error) {
      throw new Error(error);
  }
}

const getAddressService = async (payload) => {
  try {
      const procedure = `CALL spGetAddress(?)`;
      const [row] = await db.promise().query(procedure, JSON.stringify(payload));
      if (row) {
          return row;
      } else {
          return null;
      }
  } catch (error) {
      throw new Error(error);
  }
}

const deleteAddressService = async (payload) => {
  try {
    const procedure = `CALL spDeleteAddress(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error", error);
  }
}

const getUsersListService = async (payload) => {
  try {
      const procedure = `CALL spGetUsersList(?)`;
      const [row] = await db.promise().query(procedure, JSON.stringify(payload));
      if (row) {
          return row;
      } else {
          return null;
      }
  } catch (error) {
      throw new Error(error);
  }
}

const deleteUserImageService = async (fileName) => {
  try {
    const procedure = `CALL spDeleteUserImage(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(fileName));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

const saveDeviceTokenService = async (fileName) => {
  const { user_id, user_firebase_token,temp_id } = fileName;
  try {
    const sql = `update users u SET u.firebase_id = ?,u.temp_id = ? where u.id = ?`;
    const [row] = await db.promise().query(sql, [user_firebase_token,temp_id, user_id]);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

const logoutService = async (fileName) => {
  const { user_id } = fileName;
  try {
    const sql = `update users u SET u.firebase_id = null where u.id = ?`;
    const [row] = await db.promise().query(sql, user_id);
    console.log('row', row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

const deactivateUserService = async (fileName) => {
  const { user_id , is_active} = fileName;
  console.log('is_active',is_active);
  try {
    const sql = `update users u SET u.is_active = ? where u.id = ?`;
    const [row] = await db.promise().query(sql, [is_active,user_id]);
    console.log('row', row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

const createTempidService = async (payload) => {
  const {firebase_id,device_type} = payload;
  try {
      // const procedure = `CALL spCreateTempID(?)`;
      // const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
      const sql = `insert into temp_id(firebase_id,device_type) values(?,?)`;
      const [row] = await db.promise().query(sql,[firebase_id,device_type]);  
      if (row) {
          return row;
      } else {
          return null;
      }
  } catch (error) {
      throw new Error(error);
  }
}

const requestAccountDeleteService = async (payload) => {
  try {
    const procedure = `CALL spRequestAccountDelete(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));

    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw error;
  }
};

const userConfigService = async (payload) => {
  try {
    const procedure = `CALL spUserConfig(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));

    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw error;
  }
};

const updateUserConfigService = async (payload) => {
  try {
    const procedure = `CALL spUpdateUserConfig(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));

    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw error;
  }
};

const saveDetailsForNotificationService = async (payload) => {
  try {
      const procedure = `CALL spSaveDetailsForNotification(?)`;
      const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
      if (row) {
          return row;
      } else {
          return null;
      }
  } catch (error) {
      throw error;
  }
  
}


module.exports = {verifyOtpService, sendOtpService, getUserDetailService, getOfferByUserIDService, getUserRewardsService, openOfferByUserService, claimOfferByUserService, createAddressService, getAddressService, updateAddressService, deleteAddressService, getUsersListService, deleteUserImageService, saveDeviceTokenService, logoutService, deactivateUserService, editProfileService, createTempidService,requestAccountDeleteService, userConfigService, updateUserConfigService, saveDetailsForNotificationService, loginWithGoogleIdTokenService };