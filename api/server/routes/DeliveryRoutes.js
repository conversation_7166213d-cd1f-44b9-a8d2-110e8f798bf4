const { Router } = require('express');


const { authorization } = require('../../auth/tokenValidator');
const { placeDeliveryOrder, cancelDeliveryOrder, callbackDeliveryStatus } = require('../controllers/EcommController/DeliveryController');

const router = Router();

router.post('/placeDeliveryOrder', authorization, placeDeliveryOrder);
router.post('/cancelDeliveryOrder', authorization, cancelDeliveryOrder);

router.post('/callbackDeliveryStatus', callbackDeliveryStatus);

module.exports = router;
