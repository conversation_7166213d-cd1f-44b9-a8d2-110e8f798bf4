const { Router } = require('express');
const multer = require('multer');
const { registerUser, loginUser, getUserDetails, getDailyOfferByUserID, openOfferByUser, claimOfferByUser, loginWithMobile, getUserRewards, create<PERSON>ddress, update<PERSON>ddress, getAddress, deleteAddress, getUsersList, uploadUserImage, deleteUserImage, saveDeviceToken, logout, deactivateUser, editProfile, createTempid, sendOtp, verifyOtp,requestAccountDelete, userConfig, updateUserConfig, saveDetailsForNotification, loginWithGoogleIdToken } = require('../controllers/Users/<USER>');

const { authorization } = require('../../auth/tokenValidator');
const router = Router();

router.post('/sendOtp', sendOtp);
router.post('/verifyOtp', verifyOtp);

router.post('/registerUser', registerUser);
router.post('/loginUser', loginUser);
router.post('/loginWithMobile', loginWithMobile);
router.post('/loginWithGoogleIdToken', loginWithGoogleIdToken);
router.post('/getUserDetails', authorization, getUserDetails);
router.post('/getDailyOfferByUserID', authorization, getDailyOfferByUserID);
router.post('/getUserRewards', authorization, getUserRewards);
router.post('/openOfferByUser', authorization, openOfferByUser);
router.post('/claimOfferByUser', authorization, claimOfferByUser);
router.post('/saveDetailsForNotification', authorization, saveDetailsForNotification);


router.post('/createAddress', authorization, createAddress);
router.post('/updateAddress', authorization, updateAddress);
router.post('/getAddress', authorization, getAddress);
router.post('/deleteAddress', authorization, deleteAddress);

router.post('/editProfile', authorization, editProfile);
router.post('/getUsersList', authorization, getUsersList);
router.post('/deactivateUser', authorization, deactivateUser);
router.post('/requestAccountDelete', authorization, requestAccountDelete);
router.post('/userConfig', authorization, userConfig);
router.post('/UpdateUserConfig', authorization, updateUserConfig);


router.post('/uploadUserImage', multer({ dest: 'temp/', limits: { fieldSize: 8 * 1024 * 1024 } }).single('user_img'), authorization, uploadUserImage);
router.post('/deleteUserImage', authorization, deleteUserImage);

router.post('/saveDeviceToken', authorization, saveDeviceToken);
router.post('/createTempid', createTempid);
router.post('/logout', authorization, logout);


module.exports = router;
