const { Router } = require('express');
const router = Router();
const { authorization } = require('../../auth/tokenValidator');
const { totalUsers, OrdersChart,dashboardCounts,getStores, getGofrugalPurchase } = require('../controllers/Dashboard/DashboardController');
const { registerWebhook, getWebhook } = require('../controllers/EcommController/DeliveryController');

router.post('/totalUsers', authorization, totalUsers);
router.post('/dashboardCounts', authorization, dashboardCounts);
router.post('/ordersChart', authorization, OrdersChart);
router.post('/getStores', authorization, getStores);
router.post('/registerWebhook', authorization, registerWebhook);
router.get('/getwebhooks',authorization, getWebhook);
router.post('/getGofrugalPurchase',authorization, getGofrugalPurchase);



module.exports = router;


