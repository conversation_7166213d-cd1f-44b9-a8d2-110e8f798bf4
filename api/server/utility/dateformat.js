function convertDateFormat(dateString,type) {
    const date = new Date(dateString);
  
    // Extract the individual date components
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Month is zero-indexed
    const day = String(date.getDate()).padStart(2, '0');
   
    // Construct the formatted date string
    let formattedDate=""
    if(type==="end"){
       formattedDate = `${year}-${month}-${day} 23:59:59`;

    }
    else{
       formattedDate = `${year}-${month}-${day} 00:00:00`;

    }
  
    return formattedDate;
  }


  module.exports = { convertDateFormat };