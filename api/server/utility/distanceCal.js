//This function takes in latitude and longitude of two location and returns the distance between them as the crow flies (in km)
function calcKmDistanceFn(lat1, lon1, lat2, lon2) {
  var R = 6371; // km
  var dLat = toRad(lat2 - lat1);
  var dLon = toRad(lon2 - lon1);
  var lat1 = toRad(lat1);
  var lat2 = toRad(lat2);

  var a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.sin(dLon / 2) * Math.sin(dLon / 2) * Math.cos(lat1) * Math.cos(lat2);
  var c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  var d = R * c;
  return d;
}

// Converts numeric degrees to radians
function toRad(Value) {
  return Value * Math.PI / 180;
}


function isWithinRadius(userLatLong, storeLatLong, km = 5) {
  var ky = 40000 / 360;
  var kx = Math.cos(Math.PI * storeLatLong.lat / 180.0) * ky;
  var dx = Math.abs(storeLatLong.long - userLatLong.long) * kx;
  var dy = Math.abs(storeLatLong.lat - userLatLong.lat) * ky;
  return Math.sqrt(dx * dx + dy * dy) <= km;
}

// var kalyan = { lat: 25.747151196199777, lng: 82.68373482359104 };
// var pallavo = { lat: 25.765727841253483, lng: 82.65244795597565 };

// var n = isWithinRadius(vasteras, stockholm, 3.8);

module.exports = { calcKmDistanceFn, isWithinRadius };