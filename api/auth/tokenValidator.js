const { verify } = require('jsonwebtoken');
const db = require('../config/connection');

const authorization = (req, res, next) => {
  const payload = req.body;
  const {key,secret,api_name} = payload;
  console.log('api_name',payload);
  let token = req.get('authorization');
  console.log('token',token);
  if (key && secret) {
    db.query('select * from brand where id = 1', function (error, results, fields) {
      console.log(' register results', results);
      if (error) {
        console.log('error', error);
        return res.status(200).send({
          success: false,
          msg: error.message || `Something went wrong`,
          data: {}
        });
      } else {
        if (key == results[0].key && secret == results[0].secret) {
            next();
        } else {
          return res.status(401).send({
            success: false,
            msg: 'Token does not exist',
            data: {}
          });
        }
      }
    });
  } else {
    if(token == undefined){
      return res.status(401).send({
        success: false,
        msg: 'Token does not exist',
        data: {}
      });
    }else{
      token = token.slice(7);
      verify(token, process.env.JWT_KEY, (err, decode) => {
        console.log('decode',decode);
        if (decode == undefined) {
          return res.status(401).send({
            success: false,
            msg: 'Invalid token.',
            data: {}
          });
        } else {
            next();
        }
      })
    }
  }
};

module.exports = { authorization };