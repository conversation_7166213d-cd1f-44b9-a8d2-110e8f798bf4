const config = require('dotenv');
const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');

const userRoutes = require('./server/routes/UserRoutes');
const locationRoutes = require('./server/routes/LocationRoutes');
const EcommRoutes = require('./server/routes/EcommRoutes');
const DeliveryRoutes = require('./server/routes/DeliveryRoutes');
const DashboardRoutes = require('./server/routes/DashboardRoutes');
const NotificationRoutes = require('./server/routes/NotificationRoutes');
const { getGofrugalItems } = require('./server/controllers/EcommController/ProductController');

config.config();

const app = express();
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

const port = process.env.PORT || 4444;

app.use('/api/v1/mobile', userRoutes);
app.use('/api/v1/mobile', locationRoutes);
app.use('/api/v1/ecomm', EcommRoutes);
app.use('/api/v1/delivery', DeliveryRoutes);
app.use('/api/v1/dashboard', DashboardRoutes);
app.use('/api/v1/notification', NotificationRoutes);

// when a random route is inputed
app.get('*', (req, res) => res.status(200).send({
   message: 'Welcome to Rapsap PROD API up and running...'
}));

app.listen(port, () => {
   console.log(`Server is running on PORT ${port}`);
   getGofrugalItems();
});
module.exports = app;