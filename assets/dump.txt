-- MySQL dump 10.13  Distrib 8.0.29, for Win64 (x86_64)
--
-- Host: junqfoods-prod-mysql.cb1guzis2gwm.ap-south-1.rds.amazonaws.com    Database: rapsap_uat
-- ------------------------------------------------------
-- Server version	8.0.28

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
SET @MYSQLDUMP_TEMP_LOG_BIN = @@SESSION.SQL_LOG_BIN;
SET @@SESSION.SQL_LOG_BIN= 0;

--
-- GTID state at the beginning of the backup 
--

SET @@GLOBAL.GTID_PURGED=/*!80000 '+'*/ '';

--
-- Table structure for table `banner_images`
--

DROP TABLE IF EXISTS `banner_images`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `banner_images` (
  `id` int NOT NULL AUTO_INCREMENT,
  `banner_id` int NOT NULL,
  `images` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `banners`
--

DROP TABLE IF EXISTS `banners`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `banners` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `type` varchar(255) NOT NULL,
  `url` varchar(255) DEFAULT NULL,
  `product_id` int DEFAULT NULL,
  `variant_id` int DEFAULT NULL,
  `category_id` int DEFAULT NULL,
  `sub_category_id` int DEFAULT NULL,
  `meta` varchar(255) DEFAULT NULL,
  `valid_from` datetime DEFAULT NULL,
  `valid_till` datetime DEFAULT NULL,
  `is_active` int DEFAULT '1',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `brand`
--

DROP TABLE IF EXISTS `brand`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `brand` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `user_id` int DEFAULT NULL,
  `is_active` bit(1) NOT NULL DEFAULT b'1',
  `key` varchar(50) DEFAULT NULL,
  `secret` varchar(50) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `brand_stock_config`
--

DROP TABLE IF EXISTS `brand_stock_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `brand_stock_config` (
  `id` int NOT NULL AUTO_INCREMENT,
  `brand_id` int DEFAULT NULL,
  `out_of_stock` int DEFAULT '0',
  `finishing_soon` int DEFAULT '10',
  `in_stock` int DEFAULT '100',
  `plenty` int DEFAULT '101',
  `new_in_stock` int DEFAULT NULL,
  `delivery_fee` decimal(10,2) DEFAULT NULL,
  `delivery_fee_threshold` decimal(10,2) DEFAULT NULL,
  `min_order_value` decimal(10,2) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `category`
--

DROP TABLE IF EXISTS `category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `category` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `brand_id` int DEFAULT NULL,
  `description` text,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `category_images`
--

DROP TABLE IF EXISTS `category_images`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `category_images` (
  `id` int NOT NULL AUTO_INCREMENT,
  `category_id` int NOT NULL,
  `images` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `client_integration`
--

DROP TABLE IF EXISTS `client_integration`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `client_integration` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `key` varchar(255) DEFAULT NULL,
  `secret` varchar(255) DEFAULT NULL,
  `domain` varchar(255) DEFAULT NULL,
  `type` varchar(45) NOT NULL,
  `is_active` int DEFAULT '1',
  `meta` json DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `delivery`
--

DROP TABLE IF EXISTS `delivery`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `delivery` (
  `id` int NOT NULL AUTO_INCREMENT,
  `SFX_order_id` int DEFAULT NULL,
  `track_url` text,
  `order_id` int NOT NULL,
  `status` varchar(45) DEFAULT NULL,
  `delivery_cost` double DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=88 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `inventory`
--

DROP TABLE IF EXISTS `inventory`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventory` (
  `id` int NOT NULL AUTO_INCREMENT,
  `url` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `offer_image`
--

DROP TABLE IF EXISTS `offer_image`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `offer_image` (
  `id` int NOT NULL AUTO_INCREMENT,
  `original_url` varchar(245) DEFAULT NULL,
  `thumb_url` varchar(245) DEFAULT NULL,
  `offer_id` int DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `offers`
--

DROP TABLE IF EXISTS `offers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `offers` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(245) DEFAULT NULL,
  `description` longtext,
  `coupon_code` varchar(45) DEFAULT NULL,
  `valid_from` datetime DEFAULT NULL,
  `valid_till` datetime DEFAULT NULL,
  `is_percent` int DEFAULT '0',
  `percent_discount` double DEFAULT NULL,
  `amount_discount` decimal(10,2) DEFAULT NULL,
  `product_id` int DEFAULT NULL,
  `variant_id` int DEFAULT NULL,
  `category_id` int DEFAULT NULL,
  `upto` decimal(10,2) DEFAULT NULL,
  `min_amount` decimal(10,2) DEFAULT NULL,
  `tag_id` int DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `coupon_code` (`coupon_code`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `order_details`
--

DROP TABLE IF EXISTS `order_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `order_details` (
  `id` int NOT NULL AUTO_INCREMENT,
  `order_id` int NOT NULL,
  `product_id` int NOT NULL,
  `variant_id` int DEFAULT NULL,
  `quantity` int DEFAULT NULL,
  `buying_price` decimal(10,2) DEFAULT '0.00',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `order_id` (`order_id`),
  KEY `product_id` (`product_id`),
  CONSTRAINT `order_details_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`),
  CONSTRAINT `order_details_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1024 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `order_log`
--

DROP TABLE IF EXISTS `order_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `order_log` (
  `id` int NOT NULL AUTO_INCREMENT,
  `order_id` int NOT NULL,
  `reference_id` varchar(255) DEFAULT NULL,
  `status` varchar(255) DEFAULT NULL,
  `meta` json DEFAULT NULL,
  `updated_by` int DEFAULT NULL,
  `reason` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=299 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `orders`
--

DROP TABLE IF EXISTS `orders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `orders` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `status` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `order_type` varchar(50) DEFAULT NULL,
  `payment_id` int DEFAULT NULL,
  `address_id` int DEFAULT NULL,
  `offer_id` int DEFAULT NULL,
  `brand_id` int DEFAULT NULL,
  `sub_total` decimal(10,2) DEFAULT '0.00',
  `gst` decimal(10,2) DEFAULT '0.00',
  `discount` decimal(10,2) DEFAULT '0.00',
  `delivery_cost` decimal(10,2) DEFAULT NULL,
  `grand_total` decimal(10,2) DEFAULT '0.00',
  `store_id` int DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `meta` json DEFAULT NULL,
  `pickup_otp` int DEFAULT NULL,
  `return_otp` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `address_id` (`address_id`),
  CONSTRAINT `orders_ibfk_2` FOREIGN KEY (`address_id`) REFERENCES `user_address` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=99 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `payment`
--

DROP TABLE IF EXISTS `payment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `payment` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `payment_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `provider` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `order_id` int DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  `account_no` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `reference_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `razorpay_ref_id` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `status` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `meta` json DEFAULT NULL,
  `invoice_url` varchar(245) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `order_id` (`order_id`),
  CONSTRAINT `payment_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=89 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `payment_log`
--

DROP TABLE IF EXISTS `payment_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `payment_log` (
  `id` int NOT NULL AUTO_INCREMENT,
  `payment_id` int NOT NULL,
  `reference_id` varchar(255) DEFAULT NULL,
  `status` varchar(255) DEFAULT NULL,
  `meta` json DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=133 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pincode`
--

DROP TABLE IF EXISTS `pincode`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pincode` (
  `id` int NOT NULL AUTO_INCREMENT,
  `pincode` varchar(45) DEFAULT NULL,
  `description` varchar(100) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `store_id` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=149 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `product_images`
--

DROP TABLE IF EXISTS `product_images`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `product_images` (
  `id` int NOT NULL AUTO_INCREMENT,
  `product_id` int NOT NULL,
  `variant_id` int DEFAULT NULL,
  `images` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3348 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `product_variant`
--

DROP TABLE IF EXISTS `product_variant`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `product_variant` (
  `id` int NOT NULL AUTO_INCREMENT,
  `product_id` int DEFAULT NULL,
  `ref_id` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `sku` varchar(225) DEFAULT NULL,
  `price` decimal(10,2) DEFAULT '0.00',
  `cost_price` decimal(10,2) DEFAULT '0.00',
  `mrp` decimal(10,2) DEFAULT '0.00',
  `status` varchar(100) DEFAULT NULL,
  `weight` decimal(10,2) DEFAULT NULL,
  `stock` int DEFAULT NULL,
  `is_active` int DEFAULT '1',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ref_id` (`ref_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6571 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `products`
--

DROP TABLE IF EXISTS `products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `products` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `description` text,
  `SKU` varchar(255) DEFAULT NULL,
  `brand_id` int DEFAULT NULL,
  `category_id` int DEFAULT NULL,
  `sub_category_id` int DEFAULT NULL,
  `sub_category_group_id` int DEFAULT NULL,
  `price` double DEFAULT NULL,
  `status` varchar(100) DEFAULT NULL,
  `is_active` int DEFAULT '1',
  `stock` int DEFAULT NULL,
  `meta` json DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `brand_id` (`brand_id`),
  KEY `category_id` (`category_id`),
  CONSTRAINT `products_ibfk_1` FOREIGN KEY (`brand_id`) REFERENCES `brand` (`id`),
  CONSTRAINT `products_ibfk_2` FOREIGN KEY (`category_id`) REFERENCES `category` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5444 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `review`
--

DROP TABLE IF EXISTS `review`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `review` (
  `id` int NOT NULL AUTO_INCREMENT,
  `rating` int DEFAULT NULL,
  `description` text,
  `user_id` int DEFAULT NULL,
  `product_id` int DEFAULT NULL,
  `order_id` int DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`),
  KEY `order_id` (`order_id`),
  CONSTRAINT `review_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`),
  CONSTRAINT `review_ibfk_2` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `role`
--

DROP TABLE IF EXISTS `role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `role` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(145) DEFAULT NULL,
  `description` varchar(145) DEFAULT NULL,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name_UNIQUE` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `store`
--

DROP TABLE IF EXISTS `store`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `store` (
  `id` int NOT NULL AUTO_INCREMENT,
  `code` varchar(45) DEFAULT NULL,
  `name` varchar(145) NOT NULL,
  `description` text,
  `coordinate` point DEFAULT NULL,
  `contact_no` varchar(45) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `lat` decimal(12,10) DEFAULT NULL,
  `lng` decimal(12,10) DEFAULT NULL,
  `radius` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `store_inventory`
--

DROP TABLE IF EXISTS `store_inventory`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `store_inventory` (
  `id` int NOT NULL AUTO_INCREMENT,
  `url` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `storewise_variants`
--

DROP TABLE IF EXISTS `storewise_variants`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `storewise_variants` (
  `id` int NOT NULL AUTO_INCREMENT,
  `ref_id` varchar(255) DEFAULT NULL,
  `store_id` int DEFAULT NULL,
  `price` decimal(10,2) DEFAULT '0.00',
  `cost_price` decimal(10,2) DEFAULT '0.00',
  `mrp` decimal(10,2) DEFAULT '0.00',
  `stock` int DEFAULT NULL,
  `is_active` int DEFAULT '1',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3288 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sub_category`
--

DROP TABLE IF EXISTS `sub_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sub_category` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `description` text,
  `category_id` int DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=3287 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sub_category_group`
--

DROP TABLE IF EXISTS `sub_category_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sub_category_group` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `sub_category_id` int NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sub_category_group_images`
--

DROP TABLE IF EXISTS `sub_category_group_images`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sub_category_group_images` (
  `id` int NOT NULL AUTO_INCREMENT,
  `sub_category_group_id` int NOT NULL,
  `images` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `subcategory_images`
--

DROP TABLE IF EXISTS `subcategory_images`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `subcategory_images` (
  `id` int NOT NULL AUTO_INCREMENT,
  `sub_category_id` int NOT NULL,
  `images` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tags`
--

DROP TABLE IF EXISTS `tags`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tags` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `description` varchar(245) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `temp_id`
--

DROP TABLE IF EXISTS `temp_id`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `temp_id` (
  `id` int NOT NULL AUTO_INCREMENT,
  `firebase_id` varchar(255) DEFAULT NULL,
  `device_type` varchar(15) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `firebase_id` (`firebase_id`)
) ENGINE=InnoDB AUTO_INCREMENT=48 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `temp_user`
--

DROP TABLE IF EXISTS `temp_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `temp_user` (
  `id` int NOT NULL AUTO_INCREMENT,
  `first_name` varchar(100) DEFAULT 'Rapsap User',
  `user_id` int DEFAULT NULL,
  `sent_otp` varchar(200) DEFAULT NULL,
  `mobile` varchar(50) DEFAULT NULL,
  `otp_verified` tinyint DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=61 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_address`
--

DROP TABLE IF EXISTS `user_address`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_address` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int DEFAULT NULL,
  `name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `address_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `is_default` int DEFAULT '0',
  `phone` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `address1` text CHARACTER SET utf8 COLLATE utf8_general_ci,
  `address2` text CHARACTER SET utf8 COLLATE utf8_general_ci,
  `landmark` text,
  `city` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `pincode` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `state` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `longitude` decimal(11,8) DEFAULT NULL,
  `latitude` decimal(12,10) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=137 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_offer`
--

DROP TABLE IF EXISTS `user_offer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_offer` (
  `id` int NOT NULL AUTO_INCREMENT,
  `offer_id` int NOT NULL,
  `user_id` int NOT NULL,
  `activated_at` datetime DEFAULT NULL,
  `redeemed_at` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=301 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `first_name` varchar(145) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `last_name` varchar(145) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `email` varchar(145) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `country_code` tinyint DEFAULT '91',
  `mobile` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `otp_verified` tinyint DEFAULT '0',
  `sent_otp` varchar(100) DEFAULT NULL,
  `google_uid` varchar(245) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `firebase_id` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `temp_id` int DEFAULT NULL,
  `google_token` varchar(245) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `password_reset_token` json DEFAULT NULL,
  `is_active` int DEFAULT '1',
  `gender` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `dob` datetime DEFAULT NULL,
  `role_id` int DEFAULT '3',
  `username` varchar(145) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `user_profile_image` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `password` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `ref_code` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `ref_used_code` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `is_newuser` tinyint DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=50 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wishlist`
--

DROP TABLE IF EXISTS `wishlist`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wishlist` (
  `id` int NOT NULL AUTO_INCREMENT,
  `product_id` int DEFAULT NULL,
  `variant_id` int DEFAULT NULL,
  `user_id` int DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=98 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping routines for database 'rapsap_uat'
--
/*!50003 DROP PROCEDURE IF EXISTS `dummy_by_mousmi` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `dummy_by_mousmi`(IN p_data JSON)
BEGIN

	# call spGetProducts ('{"brand_id": 3}');

	DECLARE v_keyword VARCHAR(255) DEFAULT NULL;

	DECLARE v_category_id INT DEFAULT NULL; 

    DECLARE v_sub_category_id INT DEFAULT NULL;

    DECLARE v_sub_category_group_id INT DEFAULT NULL;

    DECLARE v_user_id INT DEFAULT NULL;

	DECLARE v_size INT DEFAULT 100;

	DECLARE v_page INT DEFAULT 0;

    DECLARE v_type VARCHAR(100) DEFAULT NULL;

	DECLARE v_store_id INT DEFAULT NULL;

    DECLARE v_filter varchar(100) DEFAULT NULL;

    DECLARE v_price INT DEFAULT NULL;

--     DECLARE v_sort JSON;

--     DECLARE v_order_by VARCHAR(20) DEFAULT 'desc';

    DECLARE v_customer_rating INT DEFAULT NULL;



   

--     DECLARE v_sort JSON;

--     DECLARE v_field VARCHAR(20) DEFAULT 'created_at';

--     DECLARE v_order_by VARCHAR(20) DEFAULT 'desc';

--    	DECLARE v_order_status VARCHAR(20) DEFAULT null;

    SET v_type 		= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.type')),'category');

    SET v_keyword 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.keyword')),'');

    SET v_category_id 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.category_id'));

    SET v_sub_category_id 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sub_category_id'));

    SET v_sub_category_group_id 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sub_category_group_id'));

    SET v_user_id 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.user_id'));

	SET v_size 		= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.size')),10);

	SET v_page 		= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.page')),0) * v_size ;

    SET v_store_id 		= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.store_id')),7);

    SET v_filter  = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.filter'));

--     SET v_sort 	    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort'));

--     SET v_order_by 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort.order_by'));

     SET v_price 		= JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.price'));

    SET v_customer_rating 		= JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.customer_rating'));



	if length(v_keyword) > 0 then

		set v_size = (select count(id) from products);

		set v_page = 0;

    end if;

   

  CASE  v_type 

  when 'category' THEN

   

		SELECT SQL_CALC_FOUND_ROWS p.id as product_id, p.name, p.description,

-- 		p.SKU as sku,p.is_active,

   c.name as category, c.id as category_id,sc.id as sub_category_id,sc.name as sub_category_name,

   (SELECT count(od.id) from order_details od  where od.product_id = p.id) as popularity,

   (SELECT AVG(r.rating) from review r where r.product_id = p.id) as customer_rating,

             

--    b.name as brand_name , b.id as brand_id, 

   ifnull((select

   JSON_ARRAYAGG(JSON_OBJECT("url",pi.images, "image_id",pi.id))

--     CAST(CONCAT('[', 

--     GROUP_CONCAT(JSON_OBJECT("url",pi.images, "image_id",pi.id)),

--     ']') as JSON)

    from product_images pi where pi.product_id = p.id ),CAST(CONCAT('[',']') as JSON) ) as images,

    pv.id as variant_id,pv.name as variant_name,pv.sku as variant_sku,sv.price,sv.cost_price ,sv.mrp,
    if(sv.price != 0.00, sv.price, if(sv.cost_price != 0.00, sv.cost_price, sv.mrp)) as storing_price,
    

    pv.weight,sv.stock as variant_stock,pv.is_active as variant_is_active,

    (select count(product_id) from wishlist where user_id = v_user_id and product_id = p.id) is_favorite

--     case

--     when pv.stock <= bsc.out_of_stock AND pv.stock < bsc.finishing_soon  THEN 'out of stock'

--     when pv.stock < bsc.in_stock AND pv.stock >= bsc.finishing_soon then 'finishing soon' 

--     when pv.stock < bsc.plenty AND pv.stock >= bsc.in_stock THEN 'in stock'

--     when  pv.stock >= bsc.plenty then 'plenty'  

--     else null  

--     end as product_status

		FROM products p

-- 			JOIN products p ON  p.id = o.id

			left join category c on c.id = p.category_id

-- 			left join brand b on b.id = p.brand_id

			left join sub_category sc on sc.id = p.sub_category_id 

-- 			left join brand_stock_config bsc on bsc.brand_id  = p.brand_id 

			left join (select product_id,id,ref_id,name,sku,price,cost_price,mrp,weight,stock,is_active

                      from product_variant

                      where is_active = 1

                      group by product_id 

                      HAVING max(id)) as pv ON pv.product_id = p.id

            left join storewise_variants sv on sv.ref_id = pv.ref_id and sv.store_id = v_store_id

--             left join review r on r.product_id  = p.id 

		where p.is_active = 1 and sv.is_active = 1 and

		(p.category_id = v_category_id and p.name like concat('%',v_keyword,'%'))

        ORDER BY 

        CASE v_filter WHEN 'price_low_to_high' THEN storing_price END ASC,

        CASE v_filter WHEN 'price_high_to_low' THEN storing_price END DESC,

        CASE v_filter WHEN 'popularity' THEN popularity END DESC,

        CASE v_filter WHEN 'customer_rating' THEN customer_rating END DESC,
        

        

--         CASE 

--         	WHEN v_category_id is not NULL THEN c.id = v_category_id

--         	(CASE v_filter

--         	WHEN 'price_low_to_high'

--         	then 

--              (select p.*, pv.price, pv.mrp from products p 

-- --               INNER JOIN review AS r ON r.product_id = p.id

--               inner join product_variant pv on pv.product_id = p.id

--               order by pv.price ASC)

--             WHEN 'price_high_to_low'

--             then 

--              (select p.*, pv.price, pv.mrp from products p 

-- --               INNER JOIN review AS r ON r.product_id = p.id

--               inner join product_variant pv on pv.product_id = p.id

--               order by pv.price DESC)

--             WHEN 'customer_rating'

--             then

--             (select p.*, r.rating from products p 

--             INNER JOIN review AS r ON r.product_id = p.id

--             where r.rating = v_customer_rating)

--             WHEN 'popularity'

--             then 

--             (SELECT p.*, od.product_id, count(p.id) from products p 

--             inner join order_details od on od.product_id = p.id

--             order by od.product_id)

--             WHEN  'relevance'

--             THEN 

--             (SELECT p.*, c2.id from products p

--             inner join category c2 on c2.id = p.category_id

--             where c2.id = v_category_id)

--         END ),

--         END CASE,

        p.created_at desc

		LIMIT v_page, v_size;

	   SELECT FOUND_ROWS() as total;  

	  

WHEN 'sub_category' THEN 



		SELECT SQL_CALC_FOUND_ROWS p.id as product_id, p.name, p.description,

-- 		p.SKU as sku,p.is_active,p.meta,

   c.name as category, c.id as category_id,sc.id as sub_category_id,sc.name as sub_category_name,

   (SELECT count(od.id) from order_details od  where od.product_id = p.id) as popularity,

   (SELECT AVG(r.rating) from review r where r.product_id = p.id) as customer_rating,

   ifnull((select

   JSON_ARRAYAGG(JSON_OBJECT("url",pi.images, "image_id",pi.id))

--     CAST(CONCAT('[', 

--     GROUP_CONCAT(JSON_OBJECT("url",pi.images, "image_id",pi.id)),

--     ']') as JSON)

    from product_images pi where pi.product_id = p.id ),CAST(CONCAT('[',']') as JSON) ) as images,

    pv.id as variant_id,pv.name as variant_name,pv.sku as variant_sku,sv.price,sv.cost_price ,sv.mrp,

    pv.weight,sv.stock as variant_stock,pv.is_active as variant_is_active,

    (select count(product_id) from wishlist where user_id = v_user_id and product_id = p.id) is_favorite

		FROM products p

			left join category c on c.id = p.category_id

			left join sub_category sc on sc.id = p.sub_category_id 

			left join (select product_id,id,ref_id,name,sku,price,cost_price,mrp,weight,stock,is_active

                      from product_variant

                      where is_active = 1

                      group by product_id 

                      HAVING max(id)) as pv ON pv.product_id = p.id

            left join storewise_variants sv on sv.ref_id = pv.ref_id and sv.store_id = v_store_id

--              left join review r on r.product_id  = p.id 

		where p.is_active = 1 and sv.is_active = 1 and 

		 (CASE

                  WHEN v_sub_category_id != 0

                     THEN  

                    (p.category_id = v_category_id and p.sub_category_id = v_sub_category_id and p.name like concat('%',v_keyword,'%'))

                  ELSE

                   (p.category_id = v_category_id and p.name like concat('%',v_keyword,'%'))

		 END )

        ORDER BY 

        CASE v_filter WHEN 'price_low_to_high' THEN sv.price END ASC,

        CASE v_filter WHEN 'price_high_to_low' THEN sv.price END DESC,

        CASE v_filter WHEN 'popularity' THEN popularity END DESC,

        CASE v_filter WHEN 'customer_rating' THEN customer_rating END DESC,

        

        p.created_at desc

		LIMIT v_page, v_size;

	   SELECT FOUND_ROWS() as total;  

	  

	  WHEN 'sub_category_group' THEN 



		SELECT SQL_CALC_FOUND_ROWS p.id as product_id, p.name, p.description,

-- 		p.SKU as sku,p.is_active,p.meta,

   c.name as category, c.id as category_id,sc.id as sub_category_id,sc.name as sub_category_name,

   scg.id as sub_category_group_id,scg.name as sub_category_group_name,

   (SELECT count(od.id) from order_details od  where od.product_id = p.id) as popularity,

   (SELECT AVG(r.rating) from review r where r.product_id = p.id) as customer_rating,

   ifnull((select

   JSON_ARRAYAGG(JSON_OBJECT("url",pi.images, "image_id",pi.id))

--     CAST(CONCAT('[', 

--     GROUP_CONCAT(JSON_OBJECT("url",pi.images, "image_id",pi.id)),

--     ']') as JSON)

    from product_images pi where pi.product_id = p.id ),CAST(CONCAT('[',']') as JSON) ) as images,

    pv.id as variant_id,pv.name as variant_name,pv.sku as variant_sku,sv.price,sv.cost_price ,sv.mrp,

    pv.weight,sv.stock as variant_stock,pv.is_active as variant_is_active,

    (select count(product_id) from wishlist where user_id = v_user_id and product_id = p.id) is_favorite

		FROM products p

			left join category c on c.id = p.category_id

			left join sub_category sc on sc.id = p.sub_category_id 

			left join sub_category_group scg on scg.sub_category_id = sc.id

			left join (select product_id,id,ref_id,name,sku,price,cost_price,mrp,weight,stock,is_active

                      from product_variant

                      where is_active = 1

                      group by product_id 

                      HAVING max(id)) as pv ON pv.product_id = p.id

            left join storewise_variants sv on sv.ref_id = pv.ref_id and sv.store_id = v_store_id

--             left join review r on r.product_id  = p.id 

		where p.is_active = 1 and sv.is_active = 1 and 

		 (CASE

                  WHEN v_sub_category_group_id = 0

                     THEN  

                    (p.sub_category_id = v_sub_category_id  and p.name like concat('%',v_keyword,'%'))

                  ELSE

                    (p.category_id = v_category_id and p.sub_category_id = v_sub_category_id and p.sub_category_group_id = v_sub_category_group_id and p.name like concat('%',v_keyword,'%'))

		 END )

		                     group by pv.id 

        ORDER BY 

        CASE v_filter WHEN 'price_low_to_high' THEN sv.price END ASC,

        CASE v_filter WHEN 'price_high_to_low' THEN sv.price END DESC,

        CASE v_filter WHEN 'popularity' THEN popularity END DESC,

        CASE v_filter WHEN 'customer_rating' THEN customer_rating END DESC,

        

        p.created_at desc

		LIMIT v_page, v_size;

	   SELECT FOUND_ROWS() as total;  

end case;



END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `dummy_rohit` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `dummy_rohit`(IN p_data JSON)
BEGIN

	# call spGetProducts ('{"brand_id": 3}');

	DECLARE v_keyword VARCHAR(255) DEFAULT NULL;

	DECLARE v_category_id INT DEFAULT NULL; 

    DECLARE v_sub_category_id INT DEFAULT NULL;

    DECLARE v_sub_category_group_id INT DEFAULT NULL;

    DECLARE v_user_id INT DEFAULT NULL;

	DECLARE v_size INT DEFAULT 10;

	DECLARE v_page INT DEFAULT 0;

    DECLARE v_type VARCHAR(100) DEFAULT NULL;

	DECLARE v_store_id INT DEFAULT NULL;

--     DECLARE v_sort JSON;

--     DECLARE v_field VARCHAR(20) DEFAULT 'created_at';

--     DECLARE v_order_by VARCHAR(20) DEFAULT 'desc';

--    	DECLARE v_order_status VARCHAR(20) DEFAULT null;

    SET v_type 		= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.type')),'category');

    SET v_keyword 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.keyword')),'');

    SET v_category_id 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.category_id'));

    SET v_sub_category_id 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sub_category_id'));

    SET v_sub_category_group_id 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sub_category_group_id'));

    SET v_user_id 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.user_id'));

	SET v_size 		= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.size')),10);

	SET v_page 		= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.page')),0) * v_size ;

    SET v_store_id 		= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.store_id')),7);

--     SET v_sort 	    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort'));

--     SET v_tag 		= JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.price'));

-- 	   SET v_field 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort.field'));

--     SET v_order_by 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort.order_by'));



	if length(v_keyword) > 0 then

		set v_size = (select count(id) from products);

		set v_page = 0;

    end if;

   

  CASE  v_type 

  when 'category' THEN

   

		SELECT SQL_CALC_FOUND_ROWS p.id as product_id, p.name, p.description,

-- 		p.SKU as sku,p.is_active,

   c.name as category, c.id as category_id,sc.id as sub_category_id,sc.name as sub_category_name,

--    b.name as brand_name , b.id as brand_id, 

   ifnull((select

   JSON_ARRAYAGG(JSON_OBJECT("url",pi.images, "image_id",pi.id))

--     CAST(CONCAT('[', 

--     GROUP_CONCAT(JSON_OBJECT("url",pi.images, "image_id",pi.id)),

--     ']') as JSON)

    from product_images pi where pi.product_id = p.id ),CAST(CONCAT('[',']') as JSON) ) as images,

    pv.id as variant_id,pv.name as variant_name,pv.sku as variant_sku,sv.price,sv.cost_price ,sv.mrp,

    pv.weight,sv.stock as variant_stock,pv.is_active as variant_is_active,

    (select count(product_id) from wishlist where user_id = v_user_id and product_id = p.id) is_favorite

--     case

--     when pv.stock <= bsc.out_of_stock AND pv.stock < bsc.finishing_soon  THEN 'out of stock'

--     when pv.stock < bsc.in_stock AND pv.stock >= bsc.finishing_soon then 'finishing soon' 

--     when pv.stock < bsc.plenty AND pv.stock >= bsc.in_stock THEN 'in stock'

--     when  pv.stock >= bsc.plenty then 'plenty'  

--     else null  

--     end as product_status

		FROM products p

-- 			JOIN products p ON  p.id = o.id

			left join category c on c.id = p.category_id

-- 			left join brand b on b.id = p.brand_id

			left join sub_category sc on sc.id = p.sub_category_id 

-- 			left join brand_stock_config bsc on bsc.brand_id  = p.brand_id 

			left join (select product_id,id,ref_id,name,sku,price,cost_price,mrp,weight,stock,is_active

                      from product_variant

                      where is_active = 1

                      group by product_id 

                      HAVING max(id)) as pv ON pv.product_id = p.id

            left join storewise_variants sv on sv.ref_id = pv.ref_id and sv.store_id = v_store_id

		where p.is_active = 1 and sv.is_active = 1 and

		(p.category_id = v_category_id and p.name like concat('%',v_keyword,'%'))

        ORDER BY 

        p.created_at desc

		LIMIT v_page, v_size;

	   SELECT FOUND_ROWS() as total;  

	  

WHEN 'sub_category' THEN 



		SELECT SQL_CALC_FOUND_ROWS p.id as product_id, p.name, p.description,

-- 		p.SKU as sku,p.is_active,p.meta,

   c.name as category, c.id as category_id,sc.id as sub_category_id,sc.name as sub_category_name,

   ifnull((select

   JSON_ARRAYAGG(JSON_OBJECT("url",pi.images, "image_id",pi.id))

--     CAST(CONCAT('[', 

--     GROUP_CONCAT(JSON_OBJECT("url",pi.images, "image_id",pi.id)),

--     ']') as JSON)

    from product_images pi where pi.product_id = p.id ),CAST(CONCAT('[',']') as JSON) ) as images,

    pv.id as variant_id,pv.name as variant_name,pv.sku as variant_sku,sv.price,sv.cost_price ,sv.mrp,

    pv.weight,sv.stock as variant_stock,pv.is_active as variant_is_active,

    (select count(product_id) from wishlist where user_id = v_user_id and product_id = p.id) is_favorite

		FROM products p

			left join category c on c.id = p.category_id

			left join sub_category sc on sc.id = p.sub_category_id 

			left join (select product_id,id,ref_id,name,sku,price,cost_price,mrp,weight,stock,is_active

                      from product_variant

                      where is_active = 1

                      group by product_id 

                      HAVING max(id)) as pv ON pv.product_id = p.id

            left join storewise_variants sv on sv.ref_id = pv.ref_id and sv.store_id = v_store_id

		where p.is_active = 1 and sv.is_active = 1 and

		 (CASE

                  WHEN v_sub_category_id != 0

                     THEN  

                    (p.category_id = v_category_id and p.sub_category_id = v_sub_category_id and p.name like concat('%',v_keyword,'%'))

                  ELSE

                   (p.category_id = v_category_id and p.name like concat('%',v_keyword,'%'))

		 END )

        ORDER BY 

        p.created_at desc

		LIMIT v_page, v_size;

	   SELECT FOUND_ROWS() as total;  

	  

	  WHEN 'sub_category_group' THEN 



		SELECT SQL_CALC_FOUND_ROWS p.id as product_id, p.name, p.description,

-- 		p.SKU as sku,p.is_active,p.meta,

   c.name as category, c.id as category_id,sc.id as sub_category_id,sc.name as sub_category_name,

   scg.id as sub_category_group_id,scg.name as sub_category_group_name,

   ifnull((select

   JSON_ARRAYAGG(JSON_OBJECT("url",pi.images, "image_id",pi.id))

--     CAST(CONCAT('[', 

--     GROUP_CONCAT(JSON_OBJECT("url",pi.images, "image_id",pi.id)),

--     ']') as JSON)

    from product_images pi where pi.product_id = p.id ),CAST(CONCAT('[',']') as JSON) ) as images,

    pv.id as variant_id,pv.name as variant_name,pv.sku as variant_sku,sv.price,sv.cost_price ,sv.mrp,

    pv.weight,sv.stock as variant_stock,pv.is_active as variant_is_active,

    (select count(product_id) from wishlist where user_id = v_user_id and product_id = p.id) is_favorite

		FROM products p

			left join category c on c.id = p.category_id

			left join sub_category sc on sc.id = p.sub_category_id 

			left join sub_category_group scg on scg.sub_category_id = sc.id

			left join (select product_id,id,ref_id,name,sku,price,cost_price,mrp,weight,stock,is_active

                      from product_variant

                      where is_active = 1

                      group by product_id 

                      HAVING max(id)) as pv ON pv.product_id = p.id

            left join storewise_variants sv on sv.ref_id = pv.ref_id and sv.store_id = v_store_id

		where p.is_active = 1 and sv.is_active = 1 and

		 (CASE

                  WHEN v_sub_category_group_id = 0

                     THEN  

                    (p.sub_category_id = v_sub_category_id  and p.name like concat('%',v_keyword,'%'))

                  ELSE

                    (p.category_id = v_category_id and p.sub_category_id = v_sub_category_id and p.sub_category_group_id = v_sub_category_group_id and p.name like concat('%',v_keyword,'%'))

		 END )

		                     group by pv.id 

        ORDER BY 

        p.created_at desc

		LIMIT v_page, v_size;

	   SELECT FOUND_ROWS() as total;  

end case;



END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spCancelOrder` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spCancelOrder`(IN `p_data` JSON)
BEGIN
	
	DECLARE v_order_id INT(11) DEFAULT NULL;
	DECLARE v_updated_by INT(11) DEFAULT NULL;
    DECLARE v_reason VARCHAR(128) DEFAULT NULL;

	SET v_order_id    = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.order_id'));
    SET v_updated_by  = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.updated_by'));
    SET v_reason      = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.cancel_reason'));

	START TRANSACTION;
    
		UPDATE orders o
		SET o.status     = "order_cancelled",
			o.updated_at = NOW()
		WHERE
			o.id 		 = v_order_id;

         insert into order_log(order_id,status,updated_by,reason) 
         values(v_order_id,"order_cancelled",v_updated_by,v_reason);
		
        SELECT 1 as status, 'Order cancelled successfully' as msg;
       

	COMMIT;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spCapturedPayment` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spCapturedPayment`(in p_data JSON)
BEGIN

	DECLARE v_user_id int default null;
	DECLARE v_order_id int default null;
	DECLARE v_reference_id  varchar(100) default null;
	DECLARE v_razorpay_id  varchar(100) default null;
	DECLARE v_invoice_id  varchar(100) default null;
	DECLARE v_amount DOUBLE default null;
	DECLARE v_status varchar(100) DEFAULT null;
	DECLARE v_description varchar(500) DEFAULT null;
	DECLARE v_method varchar(100) DEFAULT null;
	DECLARE v_currency varchar(100) DEFAULT null;
	DECLARE v_amount_refunded DOUBLE DEFAULT null;
	DECLARE v_captured varchar(100) DEFAULT null;
	DECLARE v_card_id varchar(200) DEFAULT null;
	DECLARE v_bank varchar(200) DEFAULT null;
	DECLARE v_wallet varchar(200) DEFAULT null;
	DECLARE v_vpa varchar(200) DEFAULT null;
	DECLARE v_email varchar(200) DEFAULT null;
	DECLARE v_contact varchar(200) DEFAULT null;
	DECLARE v_fee DOUBLE DEFAULT null;
	DECLARE v_tax DOUBLE DEFAULT null;
	DECLARE v_error_code VARCHAR(200) DEFAULT null;
	DECLARE v_error_description VARCHAR(500) DEFAULT null;
	DECLARE v_error_source VARCHAR(500) DEFAULT null;
	DECLARE v_error_step VARCHAR(500) DEFAULT null;
	DECLARE v_error_reason VARCHAR(500) DEFAULT null;
	DECLARE v_acquirer_data JSON;
	DECLARE v_created_at date DEFAULT current_timestamp();
    
    Declare v_meta JSON;
    Declare v_notes JSON;
    DECLARE v_order_meta JSON;
    DECLARE v_delivery_meta JSON;
    DECLARE v_ref_type varchar(50) DEFAULT null;
   
    Declare v_store_id INT DEFAULT null;
    Declare v_offer_id INT DEFAULT null;
    Declare v_last_payment_id INT DEFAULT null;
    Declare odr_id INT DEFAULT null;
    Declare v_delivery_cost DOUBLE DEFAULT null;
    
    -- data
    SET v_user_id			= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.user_id'));
    SET v_method 			= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.method'));
    #SET v_order_id 			= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.order_id')); 
    SET v_amount 			= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.amount'));
    SET v_reference_id 		= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.order_id')); #for razor pay order as ref
    SET v_razorpay_id 		= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.id')); #for razor pay id as ref
    SET v_status 			= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.status'));
    -- error
    SET v_error_code 		= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.error_code'));
    SET v_error_description = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.error_description'));
    SET v_error_source 		= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.error_source'));
    SET v_error_step 		= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.error_step'));
    SET v_error_reason 		= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.error_reason'));
    -- contact details
    SET v_email		 		= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.email'));
    SET v_contact		 		= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.contact'));
    
    -- payment mode
    SET v_card_id		 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.card_id'));
    SET v_bank		 		= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.bank'));
    SET v_wallet		 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.wallet'));
    SET v_vpa		 		= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.vpa'));
    
    SET v_captured		 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.captured'));
    
    SET v_notes 			= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.notes'));
    
    SET v_order_id			= JSON_UNQUOTE(JSON_EXTRACT(v_notes,'$.order_id')); #extract from notes
    SET v_user_id			= JSON_UNQUOTE(JSON_EXTRACT(v_notes,'$.user_id'));	#extract from notes
    SET v_delivery_cost			= JSON_UNQUOTE(JSON_EXTRACT(v_notes,'$.delivery_cost'));	#extract from notes
    
    -- acquirer_data for logs
    
    SET v_delivery_meta = (SELECT JSON_OBJECT("SDF_delivery_cost",v_delivery_cost));
	
    SET v_meta =	(SELECT JSON_OBJECT(
				"email", v_email,
				"contact", v_contact,
                "card", v_card_id,
                "bank", v_bank,
                "wallet", v_wallet,
                "vpa", v_vpa,
				"error_code" , v_error_code, 
                "error_description", v_error_description,
                "error_source",v_error_source,
                "error_step",v_error_step,
                "error_reason",v_error_reason,
                "notes",v_notes
                )); 
    
	--  
    
    START TRANSACTION;
   
    
    #INSERT INTO `payment`(`user_id`,`payment_type`,`provider`,`order_id`,`amount`,`account_no`,`reference_id`,`status`,`meta`) VALUES ( v_user_id, v_method,'Razor Pay', v_order_id, v_amount, null, v_reference_id, v_status,v_meta);
	#SET v_last_payment_id	= LAST_INSERT_ID();		
    
    #update payment log table
    SET v_last_payment_id = (select id from payment WHERE reference_id = v_reference_id);
    #id, payment_id, reference_id, status, created_at, updated_at, meta
    INSERT INTO payment_log (payment_id, reference_id, status, meta) values (v_last_payment_id,v_reference_id,v_status, v_meta);
    
    #update payment main table from razor pay
    update payment set status = v_status, payment_type = v_method, razorpay_ref_id = v_razorpay_id, updated_at = current_timestamp(), meta = v_meta 
    WHERE reference_id = v_reference_id;
	SET odr_id = (select id from orders where payment_id = (select id from payment where reference_id = v_reference_id));
	#update order status
    if v_captured = 'true' then
		update orders set status = 'order_paid', updated_at = current_timestamp(), meta = v_delivery_meta
		where id = odr_id;
	    UPDATE product_variant pv,
         (select o.id,od.variant_id as variant_id  ,o.status,pv.stock, od.quantity,(pv.stock - od.quantity) as remaining_stock from orders o 
          left join order_details od on od.order_id =o.id
          left join product_variant pv on pv.id = od.variant_id 
          where o.status ="order_paid" and o.id = odr_id
         ) src 
        SET pv.stock = src.remaining_stock
        WHERE pv.id = src.variant_id;
       
       	UPDATE storewise_variants pv,
         (select o.id,o.store_id,od.variant_id as variant_id ,pv.stock,pv.ref_id, od.quantity,(pv.stock - od.quantity) as remaining_stock from orders o 
          left join order_details od on od.order_id =o.id
          left join product_variant pv on pv.id = od.variant_id 
          where o.status ="order_paid" and o.id = odr_id
         ) src 
        SET pv.stock = src.remaining_stock,
            pv.updated_at = NOW()
        WHERE pv.store_id = src.store_id and pv.ref_id = src.ref_id; 
       
       
       
       UPDATE user_offer o ,
         (select o.id,offer_id from orders o 
          where o.id = odr_id
         ) src 
        SET  o.redeemed_at  = NOW()
        WHERE o.offer_id = src.offer_id and o.user_id = v_user_id;     
       
        else 
        update orders set status = 'order_failed', updated_at = current_timestamp()
		where id = odr_id;
    end if;
   
       IF (v_razorpay_id IS NOT NULL)
       then
       SET v_ref_type = 'razorpay_payment';
       SET v_order_meta = JSON_OBJECT("ref_type",v_ref_type,"extra",v_meta);
       END IF;
      
       if v_captured = 'true' then
		insert into order_log(order_id,status,reference_id,meta) 
         values(odr_id,'order_paid',v_razorpay_id,v_order_meta);
         
        else 
        insert into order_log(order_id,status,reference_id,meta) 
         values(odr_id,'order_failed',v_razorpay_id,v_order_meta);
         
    end if;
    
	SELECT 1 as status, 'Payment status Captured successfully' as msg, v_last_payment_id,v_reference_id ,v_order_id;
    
    #select v_order_id, v_user_id, v_meta;
     
    COMMIT;
	
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spCheckServicable` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spCheckServicable`(in p_data JSON)
BEGIN
	DECLARE v_pincode VARCHAR(10) default null;

    SET v_pincode = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.pincode'));

	START TRANSACTION;
SELECT
    *
FROM
    pincode
WHERE
    pincode = v_pincode;
    COMMIT;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spClaimOfferByUser` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spClaimOfferByUser`(in p_data json)
BEGIN
	DECLARE v_offer_id int default null;
	DECLARE v_user_id int default null;
	#DECLARE v_check int default null;

    SET v_offer_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.offer_id'));
    SET v_user_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.user_id'));

	# SET v_check	= (select count(*) from user_offer where offer_id = v_offer_id and user_id = v_user_id);

    Start transaction;
    
	insert into user_offer(offer_id, user_id, activated_at) values(v_offer_id, v_user_id, current_timestamp());
    SELECT 1 status, 'Offer activated successfully' AS msg;
    /*
    if v_check = 1
	   then
			update user_offer set activated_at = current_timestamp(), updated_at = current_timestamp()
            where offer_id = v_offer_id and user_id = v_user_id;

			SELECT 1 status, 'Offer activated successfully' AS msg;
		else
			select 0 status, 'Invalid request' as msg;
    end if;
    */
    commit;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spCreateAddress` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spCreateAddress`(IN `p_data` JSON)
BEGIN

	#call spCreateAddress('{"name":"rajesh from proc2",  "description":"description for a brand"}');

	DECLARE v_address_id INT(11) DEFAULT NOT NULL;
	DECLARE v_user_id INT(11) DEFAULT NOT NULL;
	DECLARE v_address1 TEXT;
    DECLARE v_address2 TEXT;
    DECLARE v_landmark TEXT;
    DECLARE v_city varchar(255) DEFAULT NULL;
    DECLARE v_pincode INT(255) DEFAULT NULL;
    DECLARE v_state varchar(255) DEFAULT NULL;
    DECLARE v_name varchar(50) DEFAULT NULL;
    DECLARE v_address_type varchar(50) DEFAULT NULL;
    DECLARE v_is_default int(5) DEFAULT 0;
	DECLARE v_phone varchar(50) DEFAULT NULL;
    DECLARE v_longitude decimal(11,8) DEFAULT NULL;
    DECLARE v_latitude decimal(12,10) DEFAULT NULL;
	
	
    SET v_user_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.user_id'));
	SET v_address1 = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.address1'));
	SET v_address2 = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.address2'));
	SET v_landmark   = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.landmark'));
	SET v_city = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.city'));
	SET v_pincode = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.pincode'));
	SET v_state = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.state'));
    SET v_name = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.name'));
    SET v_address_type = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.address_type'));
    SET v_is_default = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.is_default')),0);
    SET v_phone = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.phone'));
    SET v_longitude = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.longitude'));
    SET v_latitude = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.latitude'));


	START TRANSACTION;
		 
		insert into user_address (user_id,address1,address2,landmark,city,pincode,state,name,address_type,is_default,phone,longitude,latitude) 
	    values(v_user_id,v_address1, v_address2,v_landmark,v_city,v_pincode,v_state,v_name,v_address_type,v_is_default,v_phone,v_longitude,v_latitude);
	   
	   SET v_address_id = LAST_INSERT_ID();
	   
	  if v_is_default = 1 then
	  update user_address ua
	  set ua.is_default = 0 
	  where ua.user_id = v_user_id and ua.id != v_address_id;
	 end if;
		
		SELECT 1 AS flag, 'Inserted Successfully' AS msg;
        
	COMMIT;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spCreateBanner` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spCreateBanner`(IN `p_data` JSON)
BEGIN

	DECLARE v_name VARCHAR(255) DEFAULT NOT NULL;
	DECLARE v_description VARCHAR(255) DEFAULT NULL;
    DECLARE v_type VARCHAR(255) DEFAULT NOT NULL;
    DECLARE v_url VARCHAR(255) DEFAULT NULL;
    DECLARE v_product_id int DEFAULT NULL;
    DECLARE v_variant_id int DEFAULT NULL;
    DECLARE v_category_id int DEFAULT NULL;
    DECLARE v_sub_category_id int DEFAULT NULL;
    DECLARE v_meta VARCHAR(255) DEFAULT NULL;
    DECLARE v_valid_from datetime DEFAULT NULL;
    DECLARE v_valid_till datetime DEFAULT NULL;
    DECLARE v_is_active int DEFAULT 1;
    DECLARE v_banner_id INT(11) DEFAULT NOT NULL;
          
    SET v_name 		    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.name'));
    SET v_description 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.description'));
    SET v_type       	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.type'));
    SET v_url      	    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.url'));
    SET v_product_id    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.product_id'));
    SET v_variant_id    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.variant_id'));
    SET v_category_id   = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.category_id'));
    SET v_sub_category_id   = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sub_category_id'));
    SET v_meta          = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.meta'));
    SET v_valid_from    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.valid_from'));
    SET v_valid_till    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.valid_till'));
    SET v_is_active     = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.is_active')),1);
    SET v_banner_id     = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.banner_id'));
    
    START TRANSACTION;
    insert into banners (name,description,`type`,url,product_id,variant_id,category_id,sub_category_id,meta,valid_from,valid_till,is_active)
    values(v_name,if(v_description = 'null',NULL,v_description),v_type,v_url,v_product_id,v_variant_id,v_category_id,v_sub_category_id,v_meta,v_valid_from,v_valid_till,v_is_active);
    SET v_banner_id	= LAST_INSERT_ID();
    SELECT 1 as status, 'Banner created successfully' as msg,v_banner_id as banner_id;
    COMMIT;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spCreateCategory` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spCreateCategory`(IN `p_data` JSON)
BEGIN

	#call spCreateCategory('{"name":"kanchan", "brand_id":1, "description":""}');

	DECLARE v_name VARCHAR(255);
	DECLARE v_description varchar(255);
    DECLARE v_brand_id INT DEFAULT NULL; 
    DECLARE v_category_id INT DEFAULT NULL; 

   
	SET v_name = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.name'));
	SET v_description = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.description'));
    SET v_brand_id = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.brand_id')),1);
	
	START TRANSACTION;
		 
		insert into category (name,brand_id,description) values(v_name,v_brand_id,if(v_description = 'null',NULL,v_description));
		SET v_category_id	= LAST_INSERT_ID();
	
		SELECT 1 AS flag, 'Inserted Successfully' AS msg,v_category_id as category_id;
        
	COMMIT;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spCreateDeleteWishList` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spCreateDeleteWishList`(IN p_data JSON)
BEGIN

	DECLARE v_product_id INT DEFAULT NOT NULL;
    DECLARE v_variant_id INT DEFAULT NOT NULL;
	DECLARE v_user_id INT DEFAULT NOT NULL;
	DECLARE v_exist INT DEFAULT NOT NULL;
    
    SET v_product_id 	= JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.product_id'));
    SET v_variant_id 	= JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.variant_id'));
    SET v_user_id 	= JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.user_id'));
	
    SET v_exist	= (select count(id) from wishlist where user_id = v_user_id and product_id = v_product_id and variant_id = v_variant_id);


	START TRANSACTION;
		case when v_exist = 1 
				then 
                delete from wishlist where user_id = v_user_id and product_id = v_product_id and variant_id = v_variant_id;
                select 1 status, 'wishlist deleted successfully' as msg;
             else    
				insert into wishlist (product_id, variant_id, user_id) values (v_product_id,v_variant_id, v_user_id);
				select 1 status, 'wishlist added successfully' as msg;
        end case;
	COMMIT;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spCreateDeliveryOrder` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spCreateDeliveryOrder`(in p_data json)
BEGIN
	declare v_order_id int default not null;
	declare v_SFX_order_id int default not null;
	declare v_delivery_cost double default not null;
	declare v_status varchar(45) default null;
	declare v_track_url varchar(255) default null;
    
	SET v_order_id = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.order_details.client_order_id'));
	SET v_SFX_order_id = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sfx_order_id'));
	SET v_status = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.status'));
	SET v_track_url = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.track_url'));
	SET v_delivery_cost = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.delivery_cost'));

	STart TRANSACTION;
    
		INSERT INTO delivery(SFX_order_id, track_url, order_id, status, delivery_cost)
					values(v_SFX_order_id, v_track_url, v_order_id, v_status, v_delivery_cost);
		
        select 1 as status, 'msg' as 'delivery placed successfully.';
    
    commit;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spCreateDeliveryRequest` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spCreateDeliveryRequest`(IN p_data json)
BEGIN
	
    declare v_order_id int default not null;
    declare v_awb_number varchar(20) default not null;
    
    SET v_order_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.order_id'));
    SET v_awb_number = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.awb_number'));
    
	START TRANSACTION;
    
    select o.id as order_id,v_awb_number,ua.pincode ,
    ua.name as customer_name,ua.phone as customer_phone,CONCAT(ua.address1,',',ua.address2) as customer_address,
    ua.address_type,ua.city as c_city ,ua.state as c_state,o.sub_total as declared_value,if(o.order_type = 'COD',o.grand_total,0) as cod_amount, 
    o.grand_total as total_amount, if(o.order_type = 'COD',o.order_type,'Prepaid') as deliver_type,
    (select JSON_ARRAYAGG(JSON_OBJECT("product_category", c.name , "volumetric_weight",CAST(format(pv.weight/1000,2) as DECIMAL(10,2)),"client_sku_id",pv.sku, "product_name",pv.name , "price",pv.price))
    from product_variant pv
    join order_details od on pv.id = od.variant_id
    left join products p on p.id = od.product_id
    left join category c on c.id = p.category_id 
    where od.order_id = v_order_id
    ) as skus_attributes,
   (JSON_OBJECT("address",null,"pincode",null)) as pickup_address_attributes
    from orders o
    left join user_address ua on ua.id = o.address_id
    left join store s on s.id = o.store_id
    where o.id = v_order_id;
    
    
    COMMIT;


END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spCreateInventory` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spCreateInventory`(IN `p_data` JSON)
BEGIN

	#call spCreateCategory('{"name":"kanchan", "brand_id":1, "description":""}');

	DECLARE v_category VARCHAR(255);
 	DECLARE v_sub_category VARCHAR(255);
 
    DECLARE i  INT DEFAULT 0;

	
    DECLARE v_product JSON;

   
    SET v_product = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.orderDetails'));
	SET v_category = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.category'));
	SET v_sub_category = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.sub_category'));
	
	START TRANSACTION;
		 
-- INSERT INTO orders(user_id, status, payment_id, address_id, brand_id, sub_total, gst, discount, grand_total,store_id) 
-- 				 values(v_user_id,'order_created',v_payment_id, v_address_id, v_brand_id, v_sub_total, v_gst, v_discount, v_grand_total,v_store_id);
        
-- 	 SET v_order_id	= LAST_INSERT_ID();
     
     WHILE i < JSON_LENGTH(p_data) DO
     
     SET v_category = JSON_EXTRACT(p_data,CONCAT('$[',i,'].category'));
     SET v_sub_category = JSON_EXTRACT(p_data,CONCAT('$[',i,'].sub_category'));      
--      SET v_quantity = JSON_EXTRACT(v_product,CONCAT('$[',i,'].quantity'));
     
    select v_category as category,v_sub_category as sub_category;
-- 	 INSERT INTO order_details( order_id, product_id, variant_id, quantity ) values( v_order_id, v_product_id, v_variant_id, v_quantity);
--      
	 SELECT i + 1 INTO i;
-- 	
     END WHILE;
-- 
--      INSERT INTO order_log(order_id,status) values(v_order_id,'order_created');
     
    
-- 		SELECT 1 AS flag, 'Inserted Successfully' AS msg,category as category;
        
	COMMIT;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spCreateOffer` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spCreateOffer`(IN `p_data` JSON)
BEGIN

	DECLARE v_name VARCHAR(255) DEFAULT NOT NULL;
	DECLARE v_description LONGTEXT DEFAULT NULL;
	DECLARE v_coupon_code VARCHAR(50) DEFAULT NULL;
	DECLARE v_valid_from datetime DEFAULT NULL;
    DECLARE v_valid_till datetime DEFAULT NULL;
    DECLARE v_is_percent int DEFAULT 0;
    DECLARE v_percent_discount double DEFAULT NULL;
    DECLARE v_amount_discount decimal(10,2) DEFAULT NULL;
    DECLARE v_product_id int DEFAULT NULL;
    DECLARE v_variant_id int DEFAULT NULL;
    DECLARE v_category_id int DEFAULT NULL;
    DECLARE v_upto decimal(10,2) DEFAULT NULL;
    DECLARE v_min_amount decimal(10,2) DEFAULT NULL;
    DECLARE v_tag_id int DEFAULT NULL;
    DECLARE v_offer_id INT(11) DEFAULT NOT NULL;
          
    SET v_name 		    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.name'));
    SET v_description 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.description'));
    SET v_coupon_code 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.coupon_code'));
    SET v_valid_from    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.valid_from'));
    SET v_valid_till    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.valid_till'));
    SET v_is_percent    = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.is_percent')),0);
    SET v_percent_discount = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.percent_discount'));
    SET v_amount_discount  = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.amount_discount'));
    SET v_product_id    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.product_id'));
    SET v_variant_id    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.variant_id'));
    SET v_category_id   = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.category_id'));
    SET v_upto          = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.upto'));
    SET v_min_amount          = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.min_amount'));
    SET v_tag_id        = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.tag_id'));
    
    START TRANSACTION;
    insert into offers (name,description,coupon_code,valid_from,valid_till,is_percent,percent_discount,amount_discount,product_id,variant_id,category_id,upto,min_amount,tag_id)
    values(v_name,if(v_description = 'null',NULL,v_description),v_coupon_code,v_valid_from,v_valid_till,v_is_percent,v_percent_discount,v_amount_discount,v_product_id,v_variant_id,v_category_id,v_upto,v_min_amount,v_tag_id);
    SET v_offer_id	= LAST_INSERT_ID();
    SELECT 1 as status, 'Offer created successfully' as msg,v_offer_id as offer_id;
    COMMIT;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spCreateOrder` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spCreateOrder`(IN `p_data` JSON)
BEGIN

	DECLARE v_user_id int DEFAULT NOT NULL;
	DECLARE v_offer_id int DEFAULT NOT NULL;
    DECLARE v_payment_id int DEFAULT NULL;
	DECLARE v_address_id int DEFAULT NULL;
	DECLARE v_brand_id int DEFAULT NOT NULL;
    DECLARE v_store_id int DEFAULT NOT NULL;
   
   
    DECLARE i  INT DEFAULT 0;
    #for order details items
	DECLARE v_product_id int DEFAULT NOT NULL;
    DECLARE v_variant_id int DEFAULT NOT NULL;   
	DECLARE v_quantity int DEFAULT NOT NULL;
    DECLARE v_buying_price DECIMAL(10,2) DEFAULT NOT NULL;
    
	DECLARE v_sub_total DECIMAL(10,2) DEFAULT NOT NULL;
	DECLARE v_gst DECIMAL(10,2) DEFAULT NOT NULL;
	DECLARE v_discount DECIMAL(10,2) DEFAULT NOT NULL;
	DECLARE v_grand_total DECIMAL(10,2) DEFAULT NOT NULL;
	
    DECLARE v_orderDetails JSON;
    # to maintan the order_id
    DECLARE v_order_id INT(11) DEFAULT NOT NULL;
    
    SET v_user_id 		= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.user_id'));
    SET v_offer_id 		= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.offer_id'));
    SET v_payment_id 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.payment_id'));
    SET v_address_id 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.address_id'));
    SET v_brand_id 		= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.brand_id')),1);
    SET v_store_id		= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.store_id'));
    
    SET v_sub_total		= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sub_total'));
    SET v_gst 			= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.gst'));
    SET v_discount		= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.discount'));
    SET v_grand_total 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.grand_total'));
    
    SET v_orderDetails = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.orderDetails'));
    
    
    START TRANSACTION;
    
	 INSERT INTO orders(user_id, status, payment_id, address_id,offer_id, brand_id, sub_total, gst, discount, grand_total,store_id) 
				 values(v_user_id,'order_created',v_payment_id, v_address_id, v_offer_id,v_brand_id, v_sub_total, v_gst, v_discount, v_grand_total,v_store_id);
        
	 SET v_order_id	= LAST_INSERT_ID();
     
     WHILE i < JSON_LENGTH(v_orderDetails) DO
     
     SET v_product_id = JSON_EXTRACT(v_orderDetails,CONCAT('$[',i,'].product_id'));
     SET v_variant_id = JSON_EXTRACT(v_orderDetails,CONCAT('$[',i,'].variant_id'));      
     SET v_quantity = JSON_EXTRACT(v_orderDetails,CONCAT('$[',i,'].quantity'));
     SET v_buying_price = JSON_EXTRACT(v_orderDetails,CONCAT('$[',i,'].buying_price'));
     
	 INSERT INTO order_details( order_id, product_id, variant_id, quantity,buying_price ) values( v_order_id, v_product_id, v_variant_id, v_quantity,v_buying_price);
     
	 SELECT i + 1 INTO i;
	
     END WHILE;

     INSERT INTO order_log(order_id,status) values(v_order_id,'order_created');
    
     
    
     SELECT 1 as status, 'Order created successfully' as msg, v_order_id as order_id;
    COMMIT;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spCreateProduct` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spCreateProduct`(IN p_data JSON)
BEGIN
	
    DECLARE v_name VARCHAR(100) DEFAULT NULL;
	DECLARE v_description VARCHAR(255);
	DECLARE v_sku VARCHAR(100) DEFAULT NULL;
	DECLARE v_brand_id INT(11) DEFAULT NULL;
	DECLARE v_category_id INT(11) DEFAULT NULL;
    DECLARE v_sub_category_id INT(11) DEFAULT NULL;
    DECLARE v_sub_category_group_id INT(11) DEFAULT NULL;
	DECLARE v_meta JSON;

    DECLARE i  INT DEFAULT 0;
  
    DECLARE v_pref_id int(11) DEFAULT NOT NULL;
    DECLARE v_getcount int(11) DEFAULT NOT NULL;
    DECLARE v_variant_name VARCHAR(100) DEFAULT NOT NULL;
	DECLARE v_vsku VARCHAR(100) DEFAULT NOT NULL; 
    DECLARE v_price decimal(10,2) DEFAULT NULL;  
	DECLARE v_cost_price decimal(10,2) DEFAULT NOT NULL;
    DECLARE v_mrp decimal(10,2) DEFAULT NULL;
	DECLARE v_weight decimal(10,2) DEFAULT NULL;
	DECLARE v_stock INT(11) DEFAULT 0;
	
    DECLARE v_variants JSON;
    DECLARE v_product_id INT(11) DEFAULT NOT NULL;
    DECLARE v_store_id INT(11) DEFAULT NULL;
 
    SET v_name 			= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.name'));
    SET v_description	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.description'));
    SET v_sku 			= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sku'));
    SET v_brand_id 		= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.brand_id')),1);
    SET v_category_id	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.category_id'));
    SET v_sub_category_id	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sub_category_id'));
    SET v_sub_category_group_id	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sub_category_group_id'));
    SET v_meta 			= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.meta'));
   
    SET v_variants = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.variants'));
    SET v_store_id      = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.store_id'));
   
    START TRANSACTION;
   
    insert into products(name, description, brand_id, category_id,sub_category_id,sub_category_group_id, SKU, meta) 
	values(v_name, if(v_description = 'null',NULL,v_description), v_brand_id, v_category_id,v_sub_category_id, v_sub_category_group_id,v_sku, v_meta); 
		
     SET v_product_id = LAST_INSERT_ID();
     SET v_pref_id = (SELECT COUNT(*) FROM (SELECT DISTINCT pv.product_id FROM product_variant pv ) s);
     WHILE i < JSON_LENGTH(v_variants) DO
     
     SET v_variant_name = JSON_UNQUOTE(JSON_EXTRACT(v_variants,CONCAT('$[',i,'].variant_name')));
     SET v_vsku         = JSON_UNQUOTE(JSON_EXTRACT(v_variants,CONCAT('$[',i,'].variant_sku')));
     SET v_price        = JSON_EXTRACT(v_variants,CONCAT('$[',i,'].price'));
     SET v_cost_price   = JSON_EXTRACT(v_variants,CONCAT('$[',i,'].cost_price'));
     SET v_mrp          = JSON_EXTRACT(v_variants,CONCAT('$[',i,'].mrp'));
     SET v_weight       = JSON_EXTRACT(v_variants,CONCAT('$[',i,'].weight'));
     SET v_stock        = ifnull(JSON_EXTRACT(v_variants,CONCAT('$[',i,'].variant_stock')),0);
    
     SET v_getcount = (SELECT COUNT(*) FROM product_variant p where p.product_id = v_product_id ) + 1;
     
       
-- INSERT INTO product_variant(product_id,ref_id ,name,sku,price,cost_price,mrp,weight,stock )
-- 	 values( v_product_id,CONCAT('JF_','P',v_pref_id + 1,'_',v_getcount),v_variant_name,v_vsku, v_price,v_cost_price,v_mrp,v_weight,v_stock);

      
     INSERT INTO product_variant(product_id,ref_id ,name,sku,weight )
	 values( v_product_id,CONCAT('JF_','P',v_pref_id + 1,'_',v_getcount),v_variant_name,v_vsku,v_weight);

     if v_store_id IS NOT NULL
     THEN
     insert into storewise_variants (ref_id,store_id,price,cost_price,mrp,stock,is_active)
     values(CONCAT('JF_','P',v_pref_id + 1,'_',v_getcount),v_store_id,v_price,v_cost_price,v_mrp,v_stock,1);
     END IF;
     
	 SELECT i + 1 INTO i;
	
     END WHILE;
    
     SELECT 1 as status, 'Product created successfully' as msg, v_product_id as product_id;
    COMMIT;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spCreateReview` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spCreateReview`(IN `p_data` JSON)
BEGIN

	
	
	DECLARE v_rating int DEFAULT NOT NULL;
	DECLARE v_description VARCHAR(255) DEFAULT NULL;
	DECLARE v_user_id int DEFAULT NOT NULL;
	DECLARE v_product_id int DEFAULT NOT NULL;
	DECLARE v_order_id int DEFAULT NOT NULL;
   
    SET v_rating = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.rating'));
    SET v_description = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.description'));
    SET v_user_id = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.user_id'));
    SET v_product_id = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.product_id'));
    SET v_order_id = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.order_id'));
    
    START TRANSACTION;
	INSERT INTO review(rating, description, user_id, product_id, order_id)
    values(v_rating,if(v_description = 'null',NULL,v_description),v_user_id, v_product_id, v_order_id);
   
    SELECT 1 as status, 'Review created successfully' as msg;
   
    COMMIT;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spCreateSubCategory` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spCreateSubCategory`(IN `p_data` JSON)
BEGIN

	#call spCreateCategory('{"name":"kanchan", "brand_id":1, "description":""}');

	DECLARE v_name VARCHAR(255);
	DECLARE v_description varchar(255);
    DECLARE v_category_id INT DEFAULT NULL; 
    DECLARE v_sub_category_id INT DEFAULT NULL; 

	SET v_name = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.name'));
	SET v_description = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.description'));
    SET v_category_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.category_id'));
	
	START TRANSACTION;
		 
		insert ignore into sub_category (name,description,category_id) values(v_name,if(v_description = 'null',NULL,v_description),v_category_id);
		
	    SET v_sub_category_id = LAST_INSERT_ID();
	
		SELECT 1 AS flag, 'Inserted Successfully' AS msg, v_sub_category_id as sub_category_id;
        
	COMMIT;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spCreateSubCategoryGroup` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spCreateSubCategoryGroup`(IN `p_data` JSON)
BEGIN

	DECLARE v_name VARCHAR(255);
	DECLARE v_description varchar(255);
    DECLARE v_sub_category_id INT DEFAULT NULL; 
    DECLARE v_sub_category_group_id INT DEFAULT NULL; 

	SET v_name = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.name'));
	SET v_description = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.description'));
    SET v_sub_category_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.sub_category_id'));
	
	START TRANSACTION;
		 
		insert ignore into sub_category_group (name,description,sub_category_id) values(v_name,if(v_description = 'null',NULL,v_description),v_sub_category_id);
		
	    SET v_sub_category_group_id = LAST_INSERT_ID();
	
		SELECT 1 AS flag, 'Inserted Successfully' AS msg, v_sub_category_group_id as sub_category_group_id;
        
	COMMIT;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spCreateTag` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spCreateTag`(IN `p_data` JSON)
BEGIN



	DECLARE v_name VARCHAR(50) DEFAULT NULL;

	DECLARE v_description VARCHAR(255) DEFAULT NULL;

    DECLARE v_tag_id INT DEFAULT NULL; 



    SET v_name = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.name'));

    SET v_description = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.description'));



    

    START TRANSACTION;

	INSERT INTO tags(name, description)

    values(v_name,if(v_description = 'null',NULL,v_description));



    SET v_tag_id = LAST_INSERT_ID();

	

    SELECT 1 as status, 'Tag created successfully' as msg,v_tag_id as tag_id;

   

    COMMIT;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spCreateVariant` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spCreateVariant`(IN p_data JSON)
BEGIN

	

	DECLARE getCount INT(10);



	DECLARE v_variant_id INT(11) DEFAULT NULL; 

	DECLARE v_check INT(11) DEFAULT NULL; 

	DECLARE v_ref_id VARCHAR(100) DEFAULT NULL; 

	DECLARE v_pref_id VARCHAR(100) DEFAULT NULL; 



	DECLARE v_product_id INT(11) DEFAULT NULL;

    DECLARE v_variant_name VARCHAR(100) DEFAULT NOT NULL;

	DECLARE v_variant_description VARCHAR(100) DEFAULT NOT NULL;

    DECLARE v_variant_sku VARCHAR(100) DEFAULT NOT NULL; 

    DECLARE v_price decimal(10,2) DEFAULT NULL;  

	DECLARE v_cost_price decimal(10,2) DEFAULT NOT NULL;

    DECLARE v_mrp decimal(10,2) DEFAULT NULL;



	DECLARE v_weight decimal(10,2) DEFAULT NULL;

	DECLARE v_stock INT(11) DEFAULT 0;

    DECLARE v_store_id INT(11) DEFAULT NULL;

   

    SET v_ref_id    = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.ref_id'));

    SET v_variant_id    = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.variant_id'));

   

    SET v_product_id    = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.product_id'));

	SET v_variant_name 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.variant_name')),'original');

    SET v_variant_sku   = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.variant_sku'));

    SET v_price	        = CAST(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.price'))as DECIMAL(10,2));

    SET v_cost_price	= CAST(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.cost_price'))as DECIMAL(10,2));

    SET v_mrp	        = CAST(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.mrp'))as DECIMAL(10,2));

    SET v_weight        = CAST(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.weight'))as DECIMAL(10,2));

    SET v_stock         = ifnull(CAST(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.variant_stock')) as DECIMAL(10,0)),0);

    SET v_store_id      = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.store_id'));

    

   

	START TRANSACTION;

      

     IF v_ref_id IS NOT NULL

     THEN 



     INSERT INTO product_variant(product_id,ref_id, name,sku,weight )

	 values( v_product_id,v_ref_id,v_variant_name,v_variant_sku,v_weight)

	 ON DUPLICATE KEY UPDATE name = v_variant_name,sku= v_variant_sku,weight=v_weight;

	

-- 	if (select COUNT(*) from storewise_variants where store_id = v_store_id and ref_id = v_ref_id) != 0 THEN 

--      update storewise_variants set stock = v_stock,updated_at = NOW() where store_id = v_store_id and ref_id = v_ref_id;

--     ELSE

--      insert into storewise_variants (ref_id,store_id,stock,is_active) values(v_ref_id,v_store_id,v_stock,1);

-- 	END IF;



	

	ELSE

	 SET getCount = (SELECT COUNT(*) FROM product_variant where product_id = v_product_id);

	 INSERT INTO product_variant(product_id, name,sku,weight )

	 values( v_product_id,v_variant_name,v_variant_sku,v_weight);

	 

	 SET v_variant_id = LAST_INSERT_ID();

	

	 SET v_check = (select COUNT(*) from product_variant pv where pv.product_id = v_product_id );

     

    SET v_pref_id = (select SUBSTRING_INDEX(pv.ref_id, "_", 2) from product_variant pv where pv.product_id = v_product_id ORDER BY pv.id ASC limit 1);



      if v_check > 1 

       then

       

       UPDATE product_variant pv

       set pv.ref_id = CONCAT(v_pref_id,'_',getCount + 1)

       where pv.product_id = v_product_id and pv.id = v_variant_id; 

      

--        insert into storewise_variants (ref_id,store_id,stock,is_active) values(CONCAT(v_pref_id,'_',getCount + 1),v_store_id,v_stock,1);



    end if;

   

   if v_store_id IS NOT NULL

	THEN

   insert into storewise_variants (ref_id,store_id,price,cost_price,mrp,stock,is_active) values(CONCAT(v_pref_id,'_',getCount + 1),v_store_id,v_price,v_cost_price,v_mrp,v_stock,1);

   END IF;

  

    END IF;

     

     SELECT 1 as status, 'Variant created successfully' as msg, v_product_id as product_id;

      

	COMMIT;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spDailyOfferByUserID` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spDailyOfferByUserID`(in p_data json)
BEGIN



    declare v_user_id int DEFAULT null;

    declare v_check	 int DEFAULT null;

    declare v_offer_check	 int DEFAULT null;



    SET v_user_id = JSON_UNQUOTE(json_extract(p_data, '$.user_id'));



	SET v_check   =	(select count(*) from user_offer uo where uo.user_id = v_user_id and DATE(`created_at`) = CURDATE());

    SET v_offer_check = (select COUNT(oo.id) from offers oo

		                 where oo.tag_id = 1 and id not in (select offer_id from user_offer uf where uf.user_id =v_user_id));



    START TRANSACTION;



    case when v_check > 0 

    

    then 

		(select 0 as status_check);

	else 

    if(v_offer_check > 0)

		then

			(select 1 as status_check, oo.*, 

             (SELECT off.original_url FROM offer_image off

				WHERE

					off.offer_id = oo.id) AS image_url from offers oo

		where oo.tag_id = 1 and id not in (select offer_id from user_offer uf where uf.user_id =v_user_id)

		order by rand()

		limit 1);

		else

			(select 0 as status_check);

    end if;

   

    

    end case;

    COMMIT;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spDeleteAddress` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spDeleteAddress`(IN `p_data` JSON)
BEGIN



DECLARE v_address_id INT(11) DEFAULT NOT NULL;



SET v_address_id     = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.address_id'));



   

	START TRANSACTION;

    

delete from user_address u WHERE u.id = v_address_id;

        

 SELECT 1 AS flag, 'Deleted Successfully' AS msg;

	COMMIT;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spDeleteImage` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spDeleteImage`(IN `p_data` JSON)
BEGIN

	DECLARE v_product_image_id INT(11) DEFAULT NOT NULL;

    DECLARE v_category_image_id INT(11) DEFAULT NOT NULL;

    DECLARE v_sub_category_image_id INT(11) DEFAULT NOT NULL;

    DECLARE v_sub_category_group_image_id INT(11) DEFAULT NOT NULL;

    DECLARE v_banner_image_id INT(11) DEFAULT NOT NULL;

    DECLARE v_url varchar(500) DEFAULT NULL;

   

    SET v_product_image_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.product_image_id'));

    SET v_category_image_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.category_image_id'));

    SET v_sub_category_image_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.sub_category_image_id'));

    SET v_sub_category_group_image_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.sub_category_group_image_id'));

    SET v_banner_image_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.banner_image_id'));

   

    START TRANSACTION;

--  IF (v_product_image_id IS NOT NULL) 

-- 	    THEN

--             SET v_url = (select pi2.images from product_images pi2 where pi2.id = v_product_image_id);

--            DELETE  from product_images  where id = v_product_image_id;       

--   ELSE

--             SET v_url = (select ci2.images from category_images ci2 where ci2.id = v_category_image_id);

--             DELETE  from category_images where id = v_category_image_id;

--   END IF;

 

 	  IF (v_product_image_id IS NOT NULL) 

	      THEN

             SET v_url = (select pi2.images from product_images pi2 where pi2.id = v_product_image_id);

             DELETE  from product_images  where id = v_product_image_id;       

     ELSEIF (v_category_image_id IS NOT NULL)

          THEN

            SET v_url = (select ci2.images from category_images ci2 where ci2.id = v_category_image_id);

            DELETE  from category_images where id = v_category_image_id;

     ELSEIF (v_sub_category_group_image_id IS NOT NULL)

          THEN

            SET v_url = (select ci2.images from sub_category_group_images ci2 where ci2.id = v_sub_category_group_image_id);

            DELETE  from sub_category_group_images where id = v_sub_category_group_image_id;

     ELSEIF (v_sub_category_image_id IS NOT NULL)

          THEN

           SET v_url = (select si.images from subcategory_images si where si.id = v_sub_category_image_id);

           DELETE  from subcategory_images where id = v_sub_category_image_id;

     ELSE

           SET v_url = (select si.images from banner_images si where si.id = v_banner_image_id);

           DELETE  from banner_images where id = v_banner_image_id;

    END IF;

   

    SELECT 1 AS flag, 'Deleted Successfully' AS msg,v_url as url;

        

	COMMIT;



END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spDeleteUserImage` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spDeleteUserImage`(IN `p_data` JSON)
BEGIN

    DECLARE v_user_id INT(11) DEFAULT NOT NULL;

    DECLARE v_offer_image_id INT(11) DEFAULT NOT NULL;

    DECLARE v_url varchar(500) DEFAULT NULL;

   

    SET v_user_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.user_id'));

    SET v_offer_image_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.offer_image_id'));

   

    START TRANSACTION;

            

   IF (v_user_id IS NOT NULL) 

	    THEN

            SET v_url = (select u.user_profile_image from users u where u.id = v_user_id  );

            update users u

	    	set u.user_profile_image = null

	    	where u.id = v_user_id;      

        ELSE

            SET v_url = (select oi.original_url from offer_image oi where oi.id = v_offer_image_id);

            DELETE  from offer_image where id = v_offer_image_id;

  END IF;

   

    SELECT 1 AS flag, 'Deleted Successfully' AS msg,v_url as url;

        

	COMMIT;



END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spEditProfile` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spEditProfile`(IN `p_data` JSON)
BEGIN



	DECLARE v_user_id INT DEFAULT NOT NULL;

    DECLARE v_first_name VARCHAR(255) DEFAULT NULL;

	DECLARE v_last_name VARCHAR(50) DEFAULT NULL;

	DECLARE v_email VARCHAR(50) DEFAULT NULL;
	DECLARE v_password VARCHAR(255) DEFAULT NULL;

   

    SET v_user_id 		    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.user_id'));    

    SET v_first_name 		= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.first_name'));

    SET v_last_name 		= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.last_name'));

    SET v_email 	     	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.email'));
    SET v_password = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.password')),(select password from users where id=v_user_id) );

   



	START TRANSACTION;

    

		UPDATE

			 `users` u

		    SET 

		    u.first_name = v_first_name,

		    u.last_name = v_last_name,

		    u.email = v_email,
		    u.is_newuser=0,
		    u.password = v_password,	 

			u.updated_at  = NOW()

		WHERE

			u.id = v_user_id;

        

        SELECT 1 as status, 'Update profile successfully' as msg;



	COMMIT;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spExportCSV` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spExportCSV`(IN p_data JSON)
BEGIN



	



	DECLARE v_ids json;



	DECLARE v_start_date date DEFAULT adddate(curdate(),1);



	DECLARE v_end_date date DEFAULT SUBDATE(CURDATE(),1);



    



    #DECLARE v_start_date date DEFAULT null;



	#DECLARE v_end_date date DEFAULT null;



	



	SET v_ids 			= JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.ids')); 



	SET v_start_date 	= JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.start_date')); 



	SET v_end_date 		= JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.end_date')); 







	START TRANSACTION;



   



	SELECT 



			o.id AS order_id,



			o.created_at, o.sub_total as total, o.gst, o.grand_total, o.discount,



			CONCAT(IFNULL(u.first_name, ''), ' ', IFNULL(u.last_name, '')) AS 'full_name',



			u.mobile as customer_number, u.email,



			(SELECT  name FROM store s WHERE s.id = o.store_id) AS store_name,



			o.status, o.grand_total,



			CONCAT(ua.name, ua.address_type, ' - ', ua.address1, ' ', ua.address2, ' ', ua.city, ' ', ua.pincode, ' ', ua.state) 



			AS address, ua.pincode,



			GROUP_CONCAT(CONCAT('(', pv.name, ' x ', od.quantity, ')') SEPARATOR ',') AS item_list,



			SUM(od.quantity * pv.weight) AS weight



			FROM orders o



				LEFT JOIN



			order_details od ON o.id = od.order_id



				LEFT JOIN



			product_variant pv ON od.variant_id = pv.id



			LEFT JOIN



		user_address ua ON o.address_id = ua.id



			LEFT JOIN



		users u ON o.user_id = u.id



	#WHERE o.id IN (v_ids)



    where o.created_at between v_start_date and  v_end_date



	GROUP BY o.id;



    



    COMMIT;







END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spGetAddress` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spGetAddress`(IN `p_data` JSON)
BEGIN


    DECLARE v_user_id INT(11) DEFAULT NOT NULL;
    SET v_user_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.user_id'));

	START TRANSACTION;
    select ua.id as address_id,ua.name ,ua.address_type,ua.is_default,ua.phone,ua.address1 ,ua.address2 ,ua.landmark, ua.city ,ua.name ,ua.state ,ua.pincode ,ua.longitude,ua.latitude,ua.user_id ,ua.created_at ,ua.updated_at from user_address ua 
    where ua.user_id = v_user_id;

	COMMIT;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spGetBanner` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spGetBanner`(IN `p_data` JSON)
BEGIN
	
	DECLARE v_size INT(4) DEFAULT 10;
	DECLARE v_page INT(4) DEFAULT 0;
    DECLARE v_keyword VARCHAR(255) DEFAULT NULL;
    DECLARE v_sort JSON;
    DECLARE v_field VARCHAR(20) DEFAULT 'created_at';
    DECLARE v_order_by VARCHAR(20) DEFAULT 'desc';
    DECLARE v_type VARCHAR(20) DEFAULT 'admin';
	
   
   DECLARE v_start_date datetime DEFAULT NULL;
   DECLARE v_end_date datetime DEFAULT NULL;
  
    SET v_type	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.type')),'admin');
    SET v_keyword 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.keyword')),'');
   	SET v_size 		= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.size')),10);
	SET v_page 		= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.page')),0) * v_size ;
    SET v_sort 	    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort'));
	SET v_field 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort.field')),'created_at');
    SET v_order_by 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort.order_by')),'desc');
   
    SET v_start_date= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.start_date'));
	SET v_end_date 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.end_date'));
      
    
   if length(v_keyword) > 0 then
		set v_size = (select count(id) from offers o2 );
		set v_page = 0;
    end if;
   
   CASE v_type
		WHEN 'user' THEN
   
	   select SQL_CALC_FOUND_ROWS b.id as banner_id,b.*,bi.id as banner_image_id,bi.images as banner_image,
	   c.name as category_name,sc.name as sub_category_name,p.name as product_name,pv.name as variant_name from banners b
	   left join banner_images bi on bi.banner_id = b.id
	   left join category c on c.id = b.category_id
	   left join sub_category sc on sc.id = b.sub_category_id
	   left join products p on p.id = b.product_id
	   left join product_variant pv on pv.id = b.variant_id
	   where b.is_active = 1 and b.valid_till > CURRENT_TIMESTAMP()
        ORDER BY
        b.id desc
       LIMIT v_page, v_size;

     
      WHEN 'admin' THEN
		
		  select SQL_CALC_FOUND_ROWS b.id as banner_id,b.*,bi.id as banner_image_id,bi.images as banner_image,
	   c.name as category_name,sc.name as sub_category_name,p.name as product_name,pv.name as variant_name from banners b
	   left join banner_images bi on bi.banner_id = b.id
	   left join category c on c.id = b.category_id
	   left join sub_category sc on sc.id = b.sub_category_id
	   left join products p on p.id = b.product_id
	   left join product_variant pv on pv.id = b.variant_id
	   where b.is_active = 1 and
            (CASE WHEN v_start_date IS NOT NULL
            THEN 
             (DATE(b.valid_from) >= v_start_date and DATE(b.valid_till) <= v_end_date)        
            ELSE
            b.name like CONCAT('%',v_keyword,'%') or b.`type` like concat('%',v_keyword,'%')
	        END)
        ORDER BY
        CASE WHEN v_field = 'created_at' AND v_order_by='asc' THEN b.created_at  END ASC,
		CASE WHEN v_field = 'created_at' AND v_order_by='desc' THEN b.created_at END DESC,
		CASE WHEN v_field = 'banner_name' AND v_order_by='asc' THEN b.name  END ASC,
		CASE WHEN v_field = 'banner_name' AND v_order_by='desc' THEN b.name END DESC,
		CASE WHEN v_field = 'type' AND v_order_by='asc' THEN b.`type`  END ASC,
		CASE WHEN v_field = 'type' AND v_order_by='desc' THEN b.`type`  END DESC,
        b.id desc
        LIMIT v_page, v_size;
     
     END CASE;

          SELECT FOUND_ROWS() AS total;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spGetCategory` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spGetCategory`(IN p_data JSON)
BEGIN
    DECLARE v_keyword VARCHAR(255) DEFAULT NULL;
	DECLARE v_size INT(4) DEFAULT 10;
	DECLARE v_page INT(4) DEFAULT 0;
--     DECLARE v_type VARCHAR(20) DEFAULT 'admin';
    DECLARE v_sort JSON;
    DECLARE v_field VARCHAR(20) DEFAULT 'created_at';
    DECLARE v_order_by VARCHAR(20) DEFAULT 'asc';

	SET v_keyword = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.keyword')),'');
    SET v_size = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.size')),10);
	SET v_page = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.page')),0) * v_size ;
--     SET v_type = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.type')),'admin');
    SET v_sort 	    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort'));
	SET v_field 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort.field')),'created_at');
    SET v_order_by 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort.order_by')),'desc');
	
	START TRANSACTION;
		SELECT
	    SQL_CALC_FOUND_ROWS c.id as category_id,c.brand_id,b.name as brand_name,c.description, c.name , c.created_at ,c.updated_at,
	    ci.images as category_image,ci.id as image_id
        FROM category c 
        left join category_images ci on ci.category_id = c.id
	    left join brand b ON b.id = c.brand_id 
	    WHERE c.name LIKE CONCAT('%',v_keyword,'%') OR c.description  LIKE CONCAT('%',v_keyword,'%') 
	     ORDER BY
          CASE v_order_by WHEN 'asc' THEN
            CASE v_field
             WHEN 'created_at' THEN c.created_at 
             WHEN 'name' THEN c.name
            END
          END asc,
         CASE v_order_by WHEN 'desc' THEN
           CASE v_field
            WHEN 'created_at' THEN c.created_at
            WHEN 'name' THEN c.name 
           END
         END desc ,
         c.id asc  
	    LIMIT v_page,v_size ;
	    SELECT FOUND_ROWS() as total;
	COMMIT;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spGetHourlyOrders` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spGetHourlyOrders`(IN `p_data` JSON)
BEGIN
	
	DECLARE v_store_id INT DEFAULT NULL;

    SET v_store_id 		= JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.store_id'));

	START TRANSACTION;

	SELECT 
    COUNT(IF(HOUR(created_at)=0,1,NULL)) AS '00:00',
    COUNT(IF(HOUR(created_at)=1,1,NULL)) AS '1:00',
    COUNT(IF(HOUR(created_at)=2,1,NULL)) AS '2:00',
    COUNT(IF(HOUR(created_at)=3,1,NULL)) AS '3:00',
    COUNT(IF(HOUR(created_at)=4,1,NULL)) AS '4:00',
    COUNT(IF(HOUR(created_at)=5,1,NULL)) AS '5:00',
    COUNT(IF(HOUR(created_at)=6,1,NULL)) AS '6:00',
    COUNT(IF(HOUR(created_at)=7,1,NULL)) AS '7:00',
    COUNT(IF(HOUR(created_at)=8,1,NULL)) AS '8:00',
    COUNT(IF(HOUR(created_at)=9,1,NULL)) AS '9:00',
    COUNT(IF(HOUR(created_at)=10,1,NULL)) AS '10:00',
    COUNT(IF(HOUR(created_at)=11,1,NULL)) AS '11:00',
    COUNT(IF(HOUR(created_at)=12,1,NULL)) AS '12:00',
    COUNT(IF(HOUR(created_at)=13,1,NULL)) AS '13:00',
    COUNT(IF(HOUR(created_at)=14,1,NULL)) AS '14:00',
    COUNT(IF(HOUR(created_at)=15,1,NULL)) AS '15:00',
    COUNT(IF(HOUR(created_at)=16,1,NULL)) AS '16:00',
    COUNT(IF(HOUR(created_at)=17,1,NULL)) AS '17:00',
    COUNT(IF(HOUR(created_at)=18,1,NULL)) AS '18:00',
    COUNT(IF(HOUR(created_at)=19,1,NULL)) AS '19:00',
    COUNT(IF(HOUR(created_at)=20,1,NULL)) AS '20:00',
    COUNT(IF(HOUR(created_at)=21,1,NULL)) AS '21:00',
    COUNT(IF(HOUR(created_at)=22,1,NULL)) AS '22:00',
    COUNT(IF(HOUR(created_at)=23,1,NULL)) AS '23:00'
FROM orders 
WHERE DATE(created_at) = CURDATE() and
(CASE 
 WHEN v_store_id IS NOT NULL 
 THEN 
 store_id = v_store_id
 ELSE 1=1
 END)
group by CURDATE();
 COMMIT;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spGetOffers` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spGetOffers`(IN `p_data` JSON)
BEGIN
	# call spOrderList('{"brand_id": "3", "type":"brand"}');
 
	DECLARE v_type VARCHAR(20) DEFAULT 'admin';
	DECLARE v_user_id int(11) DEFAULT null;
    DECLARE v_size INT(4) DEFAULT 10;
	DECLARE v_page INT(4) DEFAULT 0;
    DECLARE v_keyword VARCHAR(255) DEFAULT NULL;
    DECLARE v_sort JSON;
    DECLARE v_field VARCHAR(20) DEFAULT 'created_at';
    DECLARE v_order_by VARCHAR(20) DEFAULT 'desc';
   
   DECLARE v_start_date datetime DEFAULT NULL;
   DECLARE v_end_date datetime DEFAULT NULL;
   
    SET v_type 		= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.type')),'admin');
    SET v_user_id 	= JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.user_id'));
    SET v_keyword 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.keyword')),'');
   	SET v_size 		= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.size')),10);
	SET v_page 		= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.page')),0) * v_size ;
    SET v_sort 	    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort'));
	SET v_field 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort.field')),'created_at');
    SET v_order_by 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort.order_by')),'desc');
   
    SET v_start_date= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.start_date'));
	SET v_end_date 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.end_date'));
    
   if length(v_keyword) > 0 then
		set v_size = (select count(id) from offers o2 );
		set v_page = 0;
    end if;
   
   CASE v_type
		WHEN 'user' THEN
   
	   select SQL_CALC_FOUND_ROWS o.id,o.*,oi.original_url as offer_image,oi.id as offer_image_id,p.id as product_id,uo.redeemed_at,
	   pv.id as variant_id,pv.name as variant_name,c.id as category_id,c.name as category_name,
	   t.id as tag_id,t.name as tag_name,t.description as tag_description ,pi.images as product_image,pi2.images as variant_image,ci.images as category_image,
	   if(p.id IS NOT NULL,p.name,p2.name) as product_name,
	   case
	    WHEN o.product_id IS NOT NULL THEN 'product'
		WHEN o.variant_id IS NOT NULL THEN 'variant'
		WHEN o.category_id IS NOT NULL THEN 'category'
		else null
		END as offer_type
-- 	   p2.name as product_name
	   from offers o 
	   left join user_offer uo on uo.offer_id = o.id and uo.user_id = v_user_id 
	   left join products p on p.id = o.product_id
	   left join product_variant pv on pv.id = o.variant_id
	   left join products p2 on p2.id = pv.product_id
	   left join category c on c.id = o.category_id
	   left join tags t on t.id = o.tag_id
	   left join offer_image oi on oi.offer_id = o.id
-- 	   left join product_images pi2 on pi2.product_id = o.product_id
-- 	   left join category_images ci on ci.category_id = o.category_id
	   left join (select product_id ,images
       from product_images
       group by product_id
       HAVING max(id)) as pi ON pi.product_id = o.product_id
       left join (select variant_id ,product_id,images
       from product_images
       group by variant_id
       HAVING max(id)) as pi2 ON pi2.variant_id = o.variant_id
       left join (select category_id ,images
       from category_images
       group by category_id
       HAVING max(id)) as ci ON ci.category_id = o.category_id
	   where uo.redeemed_at IS NULL
-- 	   and o.name like CONCAT('%',v_keyword,'%') or o.description like concat('%',v_keyword,'%') or o.coupon_code like concat('%',v_keyword,'%')
--             (CASE WHEN o.tag = 1
--             THEN 
--             uo.redeemed_at IS NULL 
--             ELSE
--             o.name like CONCAT('%',v_keyword,'%') or o.description like concat('%',v_keyword,'%') or o.coupon_code like concat('%',v_keyword,'%')
-- 	        END)
        ORDER BY
        CASE WHEN v_field = 'created_at' AND v_order_by='asc' THEN o.created_at  END ASC,
		CASE WHEN v_field = 'created_at' AND v_order_by='desc' THEN o.created_at END DESC,
		CASE WHEN v_field = 'offer_name' AND v_order_by='asc' THEN o.name  END ASC,
		CASE WHEN v_field = 'offer_name' AND v_order_by='desc' THEN o.name END DESC,
		CASE WHEN v_field = 'coupon_code' AND v_order_by='asc' THEN o.coupon_code  END ASC,
		CASE WHEN v_field = 'coupon_code' AND v_order_by='desc' THEN o.coupon_code  END DESC,
        o.id desc
       LIMIT v_page, v_size;

     
      WHEN 'admin' THEN
		
		 select SQL_CALC_FOUND_ROWS o.id,o.*,oi.original_url as offer_image,oi.id as offer_image_id,p.id as product_id,
		 pv.id as variant_id,pv.name as variant_name,c.id as category_id,c.name as category_name,
	   t.id as tag_id,t.name as tag_name,t.description as tag_description ,pi.images as product_image,pi2.images as variant_image,ci.images as category_image,
	   if(p.id IS NOT NULL,p.name,p2.name) as product_name
-- 	   p2.name as product_name
	   from offers o 
	   left join products p on p.id = o.product_id
	   left join product_variant pv on pv.id = o.variant_id
	   left join products p2 on p2.id = pv.product_id
	   left join category c on c.id = o.category_id
	   left join tags t on t.id = o.tag_id
	   left join offer_image oi on oi.offer_id = o.id
-- 	   left join product_images pi2 on pi2.product_id = o.product_id
-- 	   left join category_images ci on ci.category_id = o.category_id
	   left join (select product_id ,images
       from product_images
       group by product_id
       HAVING max(id)) as pi ON pi.product_id = o.product_id
       left join (select variant_id ,product_id,images
       from product_images
       group by variant_id
       HAVING max(id)) as pi2 ON pi2.variant_id = o.variant_id
       left join (select category_id ,images
       from category_images
       group by category_id
       HAVING max(id)) as ci ON ci.category_id = o.category_id
	   where
            (CASE WHEN v_start_date IS NOT NULL
            THEN 
             (DATE(o.valid_from) >= v_start_date and DATE(o.valid_till) <= v_end_date)        
            ELSE
            o.name like CONCAT('%',v_keyword,'%') or o.description like concat('%',v_keyword,'%') or o.coupon_code like concat('%',v_keyword,'%')
	        END)
        ORDER BY
        CASE WHEN v_field = 'created_at' AND v_order_by='asc' THEN o.created_at  END ASC,
		CASE WHEN v_field = 'created_at' AND v_order_by='desc' THEN o.created_at END DESC,
		CASE WHEN v_field = 'offer_name' AND v_order_by='asc' THEN o.name  END ASC,
		CASE WHEN v_field = 'offer_name' AND v_order_by='desc' THEN o.name END DESC,
		CASE WHEN v_field = 'coupon_code' AND v_order_by='asc' THEN o.coupon_code  END ASC,
		CASE WHEN v_field = 'coupon_code' AND v_order_by='desc' THEN o.coupon_code  END DESC,
        o.created_at desc
       LIMIT v_page, v_size;
     
     END CASE;
          SELECT FOUND_ROWS() AS total;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spGetOrdersChart` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spGetOrdersChart`(IN p_data JSON)
BEGIN
 SELECT
(DATE(NOW()) - INTERVAL `day` DAY) AS `DayDate`, 
COUNT(id) AS `count`
FROM (
    SELECT 0 AS `day`
    UNION SELECT 1
    UNION SELECT 2
    UNION SELECT 3
    UNION SELECT 4
    UNION SELECT 5
    UNION SELECT 6
    UNION SELECT 7
    UNION SELECT 8
    UNION SELECT 9
    UNION SELECT 10
    UNION SELECT 11
    UNION SELECT 12
    UNION SELECT 13
    UNION SELECT 14
    UNION SELECT 15
    UNION SELECT 16
    UNION SELECT 17
    UNION SELECT 18
    UNION SELECT 19
    UNION SELECT 20
    UNION SELECT 21
    UNION SELECT 22
    UNION SELECT 23
    UNION SELECT 24
    UNION SELECT 25
    UNION SELECT 26
    UNION SELECT 27
    UNION SELECT 28
    UNION SELECT 29
    UNION SELECT 30
) AS `week`
LEFT JOIN `users` u ON DATE(u.create_at) = (DATE(NOW()) - INTERVAL `day` DAY)
GROUP BY `DayDate`
ORDER BY `DayDate` ASC;
	       

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spGetPaymentKeys` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spGetPaymentKeys`()
BEGIN

	START TRANSACTION;
    
    SELECT * FROM client_integration where `type` = 'payment';
   
   COMMIT;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spGetPaymentList` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spGetPaymentList`(IN `p_data` JSON)
BEGIN
-- 	DECLARE v_type VARCHAR(20) DEFAULT 'admin';
	DECLARE v_size INT(4) DEFAULT 10;
	DECLARE v_page INT(4) DEFAULT 0;
    DECLARE v_keyword VARCHAR(255) DEFAULT NULL;
    DECLARE v_filter VARCHAR(255) DEFAULT NULL;
    DECLARE v_sort JSON;
    DECLARE v_field VARCHAR(20) DEFAULT 'created_at';
    DECLARE v_order_by VARCHAR(20) DEFAULT 'desc';
    DECLARE v_start_date datetime DEFAULT NULL;
    DECLARE v_end_date datetime DEFAULT NULL;
	DECLARE v_store_id int(11) DEFAULT null;
   
    SET v_store_id = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.store_id'));
   
--     SET v_type 		= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.type')),'admin');
    SET v_keyword 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.keyword')),'');
    SET v_filter 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.filter')),'');
   	SET v_size 		= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.size')),10);
	SET v_page 		= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.page')),0) * v_size ;
    SET v_sort 	    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort'));
	SET v_field 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort.field')),'created_at');
    SET v_order_by 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort.order_by')),'desc');
   
    SET v_start_date= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.start_date'));
	SET v_end_date 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.end_date'));
	
   if length(v_keyword) > 0 then
		set v_size = (select count(id) from payment p);
		set v_page = 0;
    end if;

		   select SQL_CALC_FOUND_ROWS p.id, p.id as payment_id,u.first_name,u.last_name,CONCAT(ifnull( u.first_name, ''),' ',ifnull( u.last_name,'')) as name ,u.mobile ,u.email,
		   o.brand_id,b.name as brand_name,o.store_id,s.name as store_name,s.code as store_code,
		   p.user_id ,p.payment_type ,p.provider ,p.order_id,
           p.amount,p.reference_id ,p.razorpay_ref_id ,p.status as payment_status,p.invoice_url,p.created_at,
--            (select JSON_ARRAYAGG(JSON_OBJECT("payment_log_id",pl.id,"status",pl.status,"reference_id",pl.reference_id,"created_at",created_at))
--            from payment_log pl where pl.payment_id = p.id) 
           (SELECT CAST(CONCAT(
            '[', 
           GROUP_CONCAT(JSON_OBJECT("payment_log_id",pl.id,"status",pl.status,"reference_id",pl.reference_id,"created_at",pl.created_at)),
           ']'
             ) as JSON)  from payment_log pl where pl.payment_id = p.id)
	        as PaymentLogs	
			from payment p 			   
			left join	`users` u on p.user_id = u.id 
			left join orders o on o.payment_id = p.id 
			left join brand b on b.id = o.brand_id 
			left join store s on s.id = o.store_id
            where
             (CASE 
                  WHEN v_store_id IS NOT NULL
                  THEN 
                    (CASE
                     WHEN v_start_date IS NOT NULL
                     THEN  
                    (DATE(p.created_at) >= v_start_date and DATE(p.created_at) <= v_end_date)
                   ELSE
                    (u.first_name like CONCAT('%',v_keyword,'%') or u.last_name like CONCAT('%',v_keyword,'%') or u.mobile like CONCAT('%',v_keyword,'%'))
		          END ) and   o.store_id =  v_store_id 
             ELSE 
                  (CASE
                 WHEN v_start_date IS NOT NULL
                 THEN  
                 (DATE(p.created_at) >= v_start_date and DATE(p.created_at) <= v_end_date)
                ELSE
                (u.first_name like CONCAT('%',v_keyword,'%') or u.last_name like CONCAT('%',v_keyword,'%') or u.mobile like CONCAT('%',v_keyword,'%'))
		         END )
            END )
              having  p.status like CONCAT('%',v_filter,'%')
		   ORDER BY 
		CASE WHEN v_field = 'created_at' AND v_order_by='asc' THEN p.created_at  END ASC,
		CASE WHEN v_field = 'created_at' AND v_order_by='desc' THEN p.created_at END DESC,
		CASE WHEN v_field = 'payment_type' AND v_order_by='asc' THEN p.payment_type  END ASC,
		CASE WHEN v_field = 'payment_type' AND v_order_by='desc' THEN p.payment_type END DESC,
		CASE WHEN v_field = 'payment_status' AND v_order_by='asc' THEN p.status  END ASC,
		CASE WHEN v_field = 'payment_status' AND v_order_by='desc' THEN p.status  END DESC,
        CASE WHEN v_field = 'amount' AND v_order_by='asc' THEN p.amount END ASC,
        CASE WHEN v_field = 'amount' AND v_order_by='desc' THEN p.amount END DESC, 
        CASE WHEN v_field = 'order_id' AND v_order_by='asc' THEN p.order_id END ASC,
        CASE WHEN v_field = 'order_id' AND v_order_by='desc' THEN p.order_id END DESC,
        p.created_at desc
            LIMIT v_page, v_size;
            SELECT FOUND_ROWS() AS total;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spGetProductByID` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spGetProductByID`(IN p_data JSON)
BEGIN
	DECLARE v_type VARCHAR(20) DEFAULT 'admin';
	DECLARE v_variant_id INT DEFAULT NULL;
	DECLARE v_product_id INT DEFAULT NULL;
	DECLARE v_user_id INT DEFAULT NULL;
    DECLARE v_store_id INT(11) DEFAULT NULL;
	
    SET v_type = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.type')),'admin');
    SET v_variant_id = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.variant_id'));
    SET v_product_id = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.product_id'));
    SET v_user_id = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.user_id'));
    SET v_store_id      = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.store_id'));
   
   	CASE v_type
	
	WHEN 'user' THEN
	    
-- 	     SELECT  p.id as product_id , p.name, p.description, p.SKU as sku,p.is_active,
--          p.meta,
-- 		c.name as category, c.id as category_id,sc.id as sub_category_id,sc.name as sub_category_name, b.name as brand_name, 
-- -- 		 CAST(CONCAT(
-- --          '[', 
-- --          GROUP_CONCAT
--           (select JSON_ARRAYAGG(JSON_OBJECT("variant_id",id,"variant_name",name,"variant_sku",sku,"cost_price",format(cost_price,2),
-- 		 "mrp",format(mrp,2),"weight",format(weight,2),"variant_stock",stock,"price",format(price,2),"variant_is_active",is_active,
-- 		 "images",pv2.images,"variant_status",
-- 		 (case
--          when stock >= bsc.out_of_stock AND stock < bsc.finishing_soon THEN 'out of stock'
--          when stock < bsc.in_stock 
--          AND stock >= bsc.finishing_soon
--          then 'finishing soon' 
--          when stock < bsc.plenty
--          AND stock >= bsc.in_stock 
--          THEN 'in stock'
--          when  stock >= bsc.plenty 
--          then 'plenty' 
--          else null
--          end) 
--          )) from product_variant where product_id = v_product_id) variants,
-- -- 		b.id as brand_id,
--         if(r3.product_id,'true','false') as is_reviewed,
-- -- 		(select if(EXISTS(select od.product_id from payment p 
-- --         left join order_details od on od.order_id = p.order_id 
-- --         where p.user_id = v_user_id and p.status ='captured' and od.product_id = v_product_id),'true','false'))AS is_purchased,
--         (select count(product_id) from wishlist where user_id = v_user_id and product_id = v_product_id) is_favorite,
--         ifnull(round(avg(r2.rating),1),0) as avg_rate,r3.rating
--         from products p
-- 		left join category c on c.id = p.category_id
-- 		left join brand b on b.id = p.brand_id
-- 		left join sub_category sc on sc.id = p.sub_category_id
-- 		left join brand_stock_config bsc on bsc.brand_id  = p.brand_id 
--         left join review r2 on r2.product_id = p.id
--         left join review r3 on r3.user_id = v_user_id and r3.product_id = v_product_id
--         left join (select id,product_id,pi.images as images
--                       from product_variant
--                       left join
--                       (select variant_id ,
--                       CAST(CONCAT(
--                       '[', 
--                       GROUP_CONCAT(JSON_OBJECT("url",images, "image_id",id)),
--                       ']'
--                        ) as JSON)
--                       images
--                       from product_images
--                       group by variant_id
--                       ) as pi ON pi.variant_id = id
--                  ) as pv2 ON pv2.product_id = p.id
--         
-- 	    where
--             p.id = v_product_id;
           
  
  	     SELECT SQL_CALC_FOUND_ROWS  p.id as product_id , p.name,p.is_active,p.description,
		c.name as category, c.id as category_id,sc.id as sub_category_id,sc.name as sub_category_name,
		scg.id as sub_category_group_id,scg.name as sub_category_group_name,
		JSON_ARRAYAGG(JSON_OBJECT("variant_id",pv.id,"variant_name",pv.name,"cost_price",REPLACE(FORMAT(sv.cost_price,2),',',''),
		"mrp",REPLACE(FORMAT(sv.mrp,2),',',''),"weight",REPLACE(FORMAT(pv.weight,2),',',''),"variant_stock",sv.stock,"price",
		REPLACE(FORMAT(sv.price,2),',',''),"variant_is_active",pv.is_active,"store_is_active",sv.is_active,
		"images",ifnull(pv.images,CAST(CONCAT('[',']') as JSON))
		))
		 as variants,
        if(r3.product_id,'true','false') as is_reviewed,
-- 		(select if(EXISTS(select od.product_id from payment p 
--         left join order_details od on od.order_id = p.order_id 
--         where p.user_id = v_user_id and p.status ='captured' and od.product_id = v_product_id),'true','false'))AS is_purchased,
        (select count(product_id) from wishlist where user_id = v_user_id and product_id = v_product_id) is_favorite,
        (select ifnull(round(avg(r2.rating),1),0) from review r2 where r2.product_id = v_product_id)  as avg_rate,
        r3.rating
        from products p
		left join category c on c.id = p.category_id
		left join sub_category sc on sc.id = p.sub_category_id
		left join sub_category_group scg on scg.sub_category_id = sc.id
        left join review r3 on r3.user_id = v_user_id and r3.product_id = v_product_id
         left join (select product_id ,id,ref_id,name,price,cost_price,mrp,weight,stock,is_active,pi.images as images
                      from product_variant
                      left join
                      (select variant_id ,JSON_ARRAYAGG(JSON_OBJECT("url",images, "image_id",id)) images
                      from product_images
                      group by variant_id
                      ) as pi ON pi.variant_id = id
                 ) as pv ON pv.product_id = p.id
        left join storewise_variants sv on sv.ref_id = pv.ref_id
	    where p.id = v_product_id and sv.is_active = 1 ;

		SELECT FOUND_ROWS() as total;
	
	WHEN 'admin' THEN
	
	     SELECT SQL_CALC_FOUND_ROWS p.id as product_id , p.name, p.description, p.SKU as sku,p.is_active,
         p.meta,
		c.name as category, c.id as category_id,sc.id as sub_category_id,sc.name as sub_category_name, b.name as brand_name,
		scg.id as sub_category_group_id,scg.name as sub_category_group_name,
		JSON_ARRAYAGG(JSON_OBJECT("variant_id",pv.id,"variant_name",pv.name,"variant_sku",pv.sku,"cost_price",ifnull(REPLACE(FORMAT(sv.cost_price,2),',',''),0.00),
		"mrp",ifnull(REPLACE(FORMAT(sv.mrp,2),',',''),0.00),"weight",REPLACE(FORMAT(pv.weight,2),',',''),"variant_stock",ifnull(sv.stock,0),"price",
		ifnull(REPLACE(FORMAT(sv.price,2),',',''),0.00),"variant_is_active",pv.is_active,"store_is_active",ifnull(sv.is_active,0),
		"images",ifnull(pv.images,CAST(CONCAT('[',']') as JSON)),"variant_status",
		(case
         when ifnull(sv.stock,0) >= bsc.out_of_stock AND ifnull(sv.stock,0) < bsc.finishing_soon THEN 'out of stock'
         when ifnull(sv.stock,0) < bsc.in_stock 
         AND ifnull(sv.stock,0) >= bsc.finishing_soon
         then 'finishing soon' 
         when ifnull(sv.stock,0) < bsc.plenty
         AND ifnull(sv.stock,0) >= bsc.in_stock 
         THEN 'in stock'
         when  ifnull(sv.stock,0) >= bsc.plenty 
         then 'plenty'
    else null
    end) 
		))
		 as variants,
        if(r3.product_id,'true','false') as is_reviewed,
-- 		(select if(EXISTS(select od.product_id from payment p 
--         left join order_details od on od.order_id = p.order_id 
--         where p.user_id = v_user_id and p.status ='captured' and od.product_id = v_product_id),'true','false'))AS is_purchased,
        (select count(product_id) from wishlist where user_id = v_user_id and product_id = v_product_id) is_favorite,
        (select ifnull(round(avg(r2.rating),1),0) from review r2 where r2.product_id = v_product_id)  as avg_rate,
        r3.rating
        from products p
		left join category c on c.id = p.category_id
		left join brand b on b.id = p.brand_id
		left join sub_category sc on sc.id = p.sub_category_id
		left join sub_category_group scg on scg.sub_category_id = sc.id
		left join brand_stock_config bsc on bsc.brand_id  = p.brand_id 
		left join review r3 on r3.user_id = v_user_id and r3.product_id = v_product_id
        left join (select product_id ,id,ref_id,name,sku,price,cost_price,mrp,weight,stock,is_active,pi.images as images
                      from product_variant
                      left join
                      (select variant_id ,JSON_ARRAYAGG(JSON_OBJECT("url",images, "image_id",id)) images
                      from product_images
                      group by variant_id
                      ) as pi ON pi.variant_id = id
--                       left join storewise_variants sv on sv.ref_id = 'JF_P2821_1' and sv.store_id = 7
                 ) as pv ON pv.product_id = p.id
        left join storewise_variants sv on sv.ref_id = pv.ref_id and sv.store_id = v_store_id
	    where p.id = v_product_id;

           
		SELECT FOUND_ROWS() as total;
	
	END CASE;
	
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spGetProducts` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spGetProducts`(IN p_data JSON)
BEGIN
	# call spGetProducts ('{"brand_id": 3}');
	DECLARE v_category_id INT DEFAULT NULL;
    DECLARE v_sub_category_id INT DEFAULT NULL;
	DECLARE v_keyword VARCHAR(255) DEFAULT NULL;
    DECLARE v_filter VARCHAR(255) DEFAULT NULL;
    DECLARE v_from INT DEFAULT NULL;
    DECLARE v_to INT DEFAULT NULL;
    DECLARE v_size INT DEFAULT 10;
	DECLARE v_page INT DEFAULT 0;
-- 	DECLARE v_tag INT DEFAULT NULL;
    DECLARE v_type VARCHAR(20) DEFAULT 'admin';
    DECLARE v_sort JSON;
    DECLARE v_field VARCHAR(20) DEFAULT 'created_at';
    DECLARE v_order_by VARCHAR(20) DEFAULT 'desc';
--    	DECLARE v_order_status VARCHAR(20) DEFAULT null;
   DECLARE v_store_id INT(11) DEFAULT NULL;
    
    SET v_category_id 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.category_id'));
    SET v_sub_category_id 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sub_category_id'));
    SET v_keyword 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.keyword')),'');
    SET v_filter 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.filter'));
    SET v_from	    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.from'));
    SET v_to 	    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.to'));
    SET v_size 		= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.size')),10);
	SET v_page 		= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.page')),0) * v_size ;
    SET v_type = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.type')),'admin');
    SET v_sort 	    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort'));
--     SET v_tag 		= JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.price'));
	SET v_field 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort.field'));
    SET v_order_by 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort.order_by'));
    SET v_store_id      = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.store_id'));

	if length(v_keyword || v_filter ) > 0 then
		set v_size = (select count(id) from products);
		set v_page = 0;
    end if;
   	  
	CASE v_type
	
		WHEN 'user' THEN
        SELECT SQL_CALC_FOUND_ROWS p.id as product_id, p.name,p.is_active,p.created_at
		FROM products p
		left join product_variant pv on pv.product_id = p.id 
		left join storewise_variants sv on sv.ref_id = pv.ref_id and sv.store_id = ifnull(v_store_id,7)
		where
        p.is_active = 1 and p.name like concat('%',v_keyword,'%')
        group by p.id 
        having sum(sv.is_active) > 0
        ORDER BY 
        p.created_at desc;
-- 		LIMIT v_page, v_size;
		
-- 		select * from products p 
-- 		where p.is_active = 1 and p.name like concat('%',v_keyword,'%')
-- 	
	   SELECT FOUND_ROWS() as total;
		
-- 		SELECT SQL_CALC_FOUND_ROWS p.id as product_id, p.name, p.description, p.SKU as sku,
--         p.is_active,p.meta,p.created_at, 
-- 	c.name as category, c.id as category_id,sc.id as sub_category_id,sc.name as sub_category_name, b.name as brand_name , b.id as brand_id,  
--     (select 
--     JSON_ARRAYAGG(JSON_OBJECT("url", ifnull(pi.images,''), "image_id",ifnull(pi.id,null)))
-- --        CAST(CONCAT(
-- --     '[', 
-- --     GROUP_CONCAT(JSON_OBJECT("url",pi.images, "image_id",pi.id)),
-- --     ']'
-- --      ) as JSON)
--     from product_images pi where pi.product_id = p.id ) as images,
-- --     JSON_ARRAYAGG(JSON_OBJECT("variant_name", pv.name, "variant_id",pv.id,"variant_sku",pv.sku,"price", pv.price,"cost_price",pv.cost_price,
-- --     "mrp",pv.mrp,"weight",pv.weight,"variant_stock",pv.stock,"variant_is_active",pv.is_active)) variants,
--     pv.id as variant_id,pv.name as variant_name,pv.sku as variant_sku,pv.price ,pv.cost_price ,pv.mrp,
--     pv.weight,pv.stock as variant_stock,pv.is_active as variant_is_active,
--     case
--     when pv.stock <= bsc.out_of_stock AND pv.stock < bsc.finishing_soon THEN 'out of stock'
--     when pv.stock < bsc.in_stock AND pv.stock >= bsc.finishing_soon then 'finishing soon' 
--     when pv.stock < bsc.plenty AND pv.stock >= bsc.in_stock THEN 'in stock'
--     when  pv.stock >= bsc.plenty then 'plenty' 
--     else null  
--     end  as product_status
-- 		FROM products o
-- 			JOIN products p ON  p.id = o.id
-- 			left join category c on c.id = p.category_id
-- 			left join brand b on b.id = p.brand_id
-- 			left join sub_category sc on sc.id = p.sub_category_id 
-- 			left join brand_stock_config bsc on bsc.brand_id  = p.brand_id
-- 			left join (select product_id ,
-- 			name,id,sku,price,cost_price,
--             mrp,weight,stock,is_active
--                       from product_variant
--                       where is_active = 1
--                       group by product_id 
--                       HAVING max(id)) as pv ON pv.product_id = o.id
-- -- 		where 
-- -- -- 		pv.is_active = 1 and 
-- -- 		(p.name like concat('%',v_keyword,'%') or p.SKU like concat('%',v_keyword,'%') or c.name like concat('%',v_keyword,'%') 
-- -- 		or sc.name like concat('%',v_keyword,'%'))
-- 		where (CASE
--     WHEN v_sub_category_id is not NULL THEN
--      sc.id = v_sub_category_id 
--      and pv.is_active = 1 and 
-- 		(p.name like concat('%',v_keyword,'%') or p.SKU like concat('%',v_keyword,'%') or c.name like concat('%',v_keyword,'%') 
-- 		or sc.name like concat('%',v_keyword,'%'))
--      ELSE
--         pv.is_active = 1 and 
-- 		(p.name like concat('%',v_keyword,'%') or p.SKU like concat('%',v_keyword,'%') or c.name like concat('%',v_keyword,'%') 
-- 		or sc.name like concat('%',v_keyword,'%'))
--     END)
--         ORDER BY 
--         CASE WHEN v_field = 'created_at' AND v_order_by='asc' THEN p.created_at  END ASC,
-- 		CASE WHEN v_field = 'created_at' AND v_order_by='desc' THEN p.created_at END DESC,
-- 		CASE WHEN v_field = 'stock' AND v_order_by='asc' THEN pv.stock  END ASC,
-- 		CASE WHEN v_field = 'stock' AND v_order_by='desc' THEN pv.stock END DESC,
-- 		CASE WHEN v_field = 'price' AND v_order_by='asc' THEN pv.price  END ASC,
-- 		CASE WHEN v_field = 'price' AND v_order_by='desc' THEN pv.price  END DESC,
--         CASE WHEN v_field = 'product_status' AND v_order_by='asc' THEN product_status END ASC,
--         CASE WHEN v_field = 'product_status' AND v_order_by='desc' THEN product_status END DESC,
--         CASE WHEN v_field = 'is_active' AND v_order_by='asc' THEN p.is_active END ASC,
--         CASE WHEN v_field = 'is_active' AND v_order_by='desc' THEN p.is_active END DESC,
--         p.created_at desc
-- 		LIMIT v_page, v_size;
-- 	
-- 	   SELECT FOUND_ROWS() as total;
       
		when 'admin' then
		
	   SELECT SQL_CALC_FOUND_ROWS p.id as product_id, p.name, p.description, p.SKU as sku,
--     (select SUM(pv2.stock) from product_variant pv2 where pv2.product_id = p.id ) as total_stock, 
       ifnull((select sum(sv.stock) from storewise_variants sv
       left join product_variant pv on sv.ref_id = pv.ref_id and sv.store_id = v_store_id
       where pv.product_id = p.id ),0) as total_stock, 
--        if(sv.summ > 0,1,0) as store_is_active,
--     format(pv3.max_price,2) as max_price,format(pv3.min_price,2) as min_price,
     p.is_active,p.meta,p.created_at, 
	c.name as category, c.id as category_id,sc.id as sub_category_id,sc.name as sub_category_name,scg.id as sub_category_group_id,scg.name as sub_category_group_name
--     (select JSON_ARRAYAGG(JSON_OBJECT("url", pi.images, "image_id",pi.id))
--     from product_images pi where pi.product_id = p.id ) as images,
--     pv.name as variant_name,pv.sku as variant_sku, pv.price,pv.cost_price,pv.mrp,
--     pv.weight,pv.stock as variant_stock,pv.is_active as variant_is_active,
	
--     case
--     when (select SUM(pv2.stock) from product_variant pv2 where pv2.product_id = p.id ) >= bsc.out_of_stock
--     AND (select SUM(pv2.stock) from product_variant pv2 where pv2.product_id = p.id ) < bsc.finishing_soon
--     THEN 'out of stock'
--     when (select SUM(pv2.stock) from product_variant pv2 where pv2.product_id = p.id ) < bsc.in_stock 
--     AND (select SUM(pv2.stock) from product_variant pv2 where pv2.product_id = p.id ) >= bsc.finishing_soon
--     then 'finishing soon' 
--     when (select SUM(pv2.stock) from product_variant pv2 where pv2.product_id = p.id ) < bsc.plenty
--     AND (select SUM(pv2.stock) from product_variant pv2 where pv2.product_id = p.id ) >= bsc.in_stock 
--     THEN 'in stock'
--     when  (select SUM(pv2.stock) from product_variant pv2 where pv2.product_id = p.id ) >= bsc.plenty 
--     then 'plenty' 
--     else null  
--     end  as product_status
		FROM products p
			left join category c on c.id = p.category_id
			left join sub_category sc on sc.id = p.sub_category_id
			left join sub_category_group scg on scg.sub_category_id = sc.id
-- 			left join junq_uat.product_variant pv on pv.product_id = p.id
-- 			left join junq_uat.storewise_variants sv on sv.ref_id = pv.ref_id and sv.store_id = 7
-- 			left join (select product_id ,name,sku,price,cost_price,mrp,weight,stock,is_active
--                       from product_variant
--                       group by product_id 
--                       HAVING max(id)) as pv ON pv.product_id = o.id
			
--             left join (select product_id ,max(price) as max_price,min(price) as min_price
--                       from product_variant
--                       ) as pv3 ON pv3.product_id = p.id
where
(CASE 
    WHEN v_category_id is not NULL THEN c.id = v_category_id
    WHEN v_from is not NULL THEN 

-- 	 (case v_filter
--     when  'out of stock'
--     THEN 
--     (select SUM(pv2.stock) from product_variant pv2 where pv2.product_id = p.id ) >= (select bsc2.out_of_stock from brand_stock_config bsc2 where bsc.brand_id=1)
--     AND (select SUM(pv2.stock) from product_variant pv2 where pv2.product_id = p.id ) < (select bsc2.finishing_soon from brand_stock_config bsc2 where bsc.brand_id=1) 
--     when 'finishing soon' 
--     then 
--     (select SUM(pv2.stock) from product_variant pv2 where pv2.product_id = p.id ) < (select bsc2.in_stock from brand_stock_config bsc2 where bsc.brand_id=1)
--     AND (select SUM(pv2.stock) from product_variant pv2 where pv2.product_id = p.id ) >= (select bsc2.finishing_soon from brand_stock_config bsc2 where bsc.brand_id=1)
--     when 'in stock'
--     then
--     (select SUM(pv2.stock) from product_variant pv2 where pv2.product_id = p.id ) < (select bsc2.plenty from brand_stock_config bsc2 where bsc.brand_id=1)
--     AND (select SUM(pv2.stock) from product_variant pv2 where pv2.product_id = p.id ) >= (select bsc2.in_stock from brand_stock_config bsc2 where bsc.brand_id=1) 
--     when 'plenty'
--     then
--     (select SUM(pv2.stock) from product_variant pv2 where pv2.product_id = p.id ) >= (select bsc2.plenty from brand_stock_config bsc2 where bsc.brand_id=1) 
    (case when v_to IS NOT NULL THEN
    (select sum(sv.stock) from storewise_variants sv
left join product_variant pv on sv.ref_id = pv.ref_id 
where pv.product_id = p.id ) BETWEEN v_from and v_to
    ELSE 
    (select sum(sv.stock) from storewise_variants sv
left join product_variant pv on sv.ref_id = pv.ref_id 
where pv.product_id = p.id ) >= v_from 
    END ) 
     ELSE
    (p.name like concat('%',v_keyword,'%') or p.SKU like concat('%',v_keyword,'%') or c.name like concat('%',v_keyword,'%') or sc.name like concat('%',v_keyword,'%')
    or scg.name like concat('%',v_keyword,'%'))
END)
        ORDER BY 
        CASE WHEN v_field = 'created_at' AND v_order_by='asc' THEN p.created_at  END ASC,
		CASE WHEN v_field = 'created_at' AND v_order_by='desc' THEN p.created_at END DESC,
		CASE WHEN v_field = 'total_stock' AND v_order_by='asc' THEN
		(select sum(sv.stock) from storewise_variants sv
left join product_variant pv on sv.ref_id = pv.ref_id 
where pv.product_id = p.id )  END ASC,
		CASE WHEN v_field = 'total_stock' AND v_order_by='desc' THEN 
		(select sum(sv.stock) from storewise_variants sv
left join product_variant pv on sv.ref_id = pv.ref_id 
where pv.product_id = p.id ) END DESC,
-- 		CASE WHEN v_field = 'min_price' AND v_order_by='asc' THEN pv.price  END ASC,
-- 		CASE WHEN v_field = 'min_price' AND v_order_by='desc' THEN pv.price  END DESC,
--         CASE WHEN v_field = 'product_status' AND v_order_by='asc' THEN product_status END ASC,
--         CASE WHEN v_field = 'product_status' AND v_order_by='desc' THEN product_status END DESC,
        CASE WHEN v_field = 'is_active' AND v_order_by='asc' THEN p.is_active END ASC,
        CASE WHEN v_field = 'is_active' AND v_order_by='desc' THEN p.is_active END DESC,
        p.created_at desc
		LIMIT v_page, v_size;
	
	   SELECT FOUND_ROWS() as total;
	  
	END CASE;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spGetProductsByCategory` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spGetProductsByCategory`(IN p_data JSON)
BEGIN
	# call spGetProducts ('{"brand_id": 3}');
	DECLARE v_keyword VARCHAR(255) DEFAULT NULL;
	DECLARE v_category_id INT DEFAULT NULL; 
    DECLARE v_sub_category_id INT DEFAULT NULL;
    DECLARE v_sub_category_group_id INT DEFAULT NULL;
    DECLARE v_user_id INT DEFAULT NULL;
	DECLARE v_size INT DEFAULT 10;
	DECLARE v_page INT DEFAULT 0;
    DECLARE v_type VARCHAR(100) DEFAULT NULL;
	DECLARE v_store_id INT DEFAULT NULL;
--     DECLARE v_sort JSON;
--     DECLARE v_field VARCHAR(20) DEFAULT 'created_at';
--     DECLARE v_order_by VARCHAR(20) DEFAULT 'desc';
--    	DECLARE v_order_status VARCHAR(20) DEFAULT null;
    SET v_type 		= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.type')),'category');
    SET v_keyword 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.keyword')),'');
    SET v_category_id 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.category_id'));
    SET v_sub_category_id 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sub_category_id'));
    SET v_sub_category_group_id 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sub_category_group_id'));
    SET v_user_id 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.user_id'));
	SET v_size 		= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.size')),10);
	SET v_page 		= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.page')),0) * v_size ;
    SET v_store_id 		= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.store_id')),7);
--     SET v_sort 	    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort'));
--     SET v_tag 		= JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.price'));
-- 	   SET v_field 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort.field'));
--     SET v_order_by 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort.order_by'));

	if length(v_keyword) > 0 then
		set v_size = (select count(id) from products);
		set v_page = 0;
    end if;
   
  CASE  v_type 
  when 'category' THEN
   
		SELECT SQL_CALC_FOUND_ROWS p.id as product_id, p.name, p.description,
-- 		p.SKU as sku,p.is_active,
   c.name as category, c.id as category_id,sc.id as sub_category_id,sc.name as sub_category_name,
--    b.name as brand_name , b.id as brand_id, 
   ifnull((select
   JSON_ARRAYAGG(JSON_OBJECT("url",pi.images, "image_id",pi.id))
--     CAST(CONCAT('[', 
--     GROUP_CONCAT(JSON_OBJECT("url",pi.images, "image_id",pi.id)),
--     ']') as JSON)
    from product_images pi where pi.product_id = p.id ),CAST(CONCAT('[',']') as JSON) ) as images,
    pv.id as variant_id,pv.name as variant_name,pv.sku as variant_sku,sv.price,sv.cost_price ,sv.mrp,
    pv.weight,sv.stock as variant_stock,pv.is_active as variant_is_active,
    (select count(product_id) from wishlist where user_id = v_user_id and product_id = p.id) is_favorite
--     case
--     when pv.stock <= bsc.out_of_stock AND pv.stock < bsc.finishing_soon  THEN 'out of stock'
--     when pv.stock < bsc.in_stock AND pv.stock >= bsc.finishing_soon then 'finishing soon' 
--     when pv.stock < bsc.plenty AND pv.stock >= bsc.in_stock THEN 'in stock'
--     when  pv.stock >= bsc.plenty then 'plenty'  
--     else null  
--     end as product_status
		FROM products p
-- 			JOIN products p ON  p.id = o.id
			left join category c on c.id = p.category_id
-- 			left join brand b on b.id = p.brand_id
			left join sub_category sc on sc.id = p.sub_category_id 
-- 			left join brand_stock_config bsc on bsc.brand_id  = p.brand_id 
			left join (select product_id,id,ref_id,name,sku,price,cost_price,mrp,weight,stock,is_active
                      from product_variant
                      where is_active = 1
                      group by product_id 
                      HAVING max(id)) as pv ON pv.product_id = p.id
            left join storewise_variants sv on sv.ref_id = pv.ref_id and sv.store_id = v_store_id
		where p.is_active = 1 and sv.is_active = 1 and
		(p.category_id = v_category_id and p.name like concat('%',v_keyword,'%'))
        ORDER BY 
        p.created_at desc
		LIMIT v_page, v_size;
	   SELECT FOUND_ROWS() as total;  
	  
WHEN 'sub_category' THEN 

		SELECT SQL_CALC_FOUND_ROWS p.id as product_id, p.name, p.description,
-- 		p.SKU as sku,p.is_active,p.meta,
   c.name as category, c.id as category_id,sc.id as sub_category_id,sc.name as sub_category_name,
   ifnull((select
   JSON_ARRAYAGG(JSON_OBJECT("url",pi.images, "image_id",pi.id))
--     CAST(CONCAT('[', 
--     GROUP_CONCAT(JSON_OBJECT("url",pi.images, "image_id",pi.id)),
--     ']') as JSON)
    from product_images pi where pi.product_id = p.id ),CAST(CONCAT('[',']') as JSON) ) as images,
    pv.id as variant_id,pv.name as variant_name,pv.sku as variant_sku,sv.price,sv.cost_price ,sv.mrp,
    pv.weight,sv.stock as variant_stock,pv.is_active as variant_is_active,
    (select count(product_id) from wishlist where user_id = v_user_id and product_id = p.id) is_favorite
		FROM products p
			left join category c on c.id = p.category_id
			left join sub_category sc on sc.id = p.sub_category_id 
			left join (select product_id,id,ref_id,name,sku,price,cost_price,mrp,weight,stock,is_active
                      from product_variant
                      where is_active = 1
                      group by product_id 
                      HAVING max(id)) as pv ON pv.product_id = p.id
            left join storewise_variants sv on sv.ref_id = pv.ref_id and sv.store_id = v_store_id
		where p.is_active = 1 and sv.is_active = 1 and
		 (CASE
                  WHEN v_sub_category_id != 0
                     THEN  
                    (p.category_id = v_category_id and p.sub_category_id = v_sub_category_id and p.name like concat('%',v_keyword,'%'))
                  ELSE
                   (p.category_id = v_category_id and p.name like concat('%',v_keyword,'%'))
		 END )
        ORDER BY 
        p.created_at desc
		LIMIT v_page, v_size;
	   SELECT FOUND_ROWS() as total;  
	  
	  WHEN 'sub_category_group' THEN 

		SELECT SQL_CALC_FOUND_ROWS p.id as product_id, p.name, p.description,
-- 		p.SKU as sku,p.is_active,p.meta,
   c.name as category, c.id as category_id,sc.id as sub_category_id,sc.name as sub_category_name,
   scg.id as sub_category_group_id,scg.name as sub_category_group_name,
   ifnull((select
   JSON_ARRAYAGG(JSON_OBJECT("url",pi.images, "image_id",pi.id))
--     CAST(CONCAT('[', 
--     GROUP_CONCAT(JSON_OBJECT("url",pi.images, "image_id",pi.id)),
--     ']') as JSON)
    from product_images pi where pi.product_id = p.id ),CAST(CONCAT('[',']') as JSON) ) as images,
    pv.id as variant_id,pv.name as variant_name,pv.sku as variant_sku,sv.price,sv.cost_price ,sv.mrp,
    pv.weight,sv.stock as variant_stock,pv.is_active as variant_is_active,
    (select count(product_id) from wishlist where user_id = v_user_id and product_id = p.id) is_favorite
		FROM products p
			left join category c on c.id = p.category_id
			left join sub_category sc on sc.id = p.sub_category_id 
			left join sub_category_group scg on scg.sub_category_id = sc.id
			left join (select product_id,id,ref_id,name,sku,price,cost_price,mrp,weight,stock,is_active
                      from product_variant
                      where is_active = 1
                      group by product_id 
                      HAVING max(id)) as pv ON pv.product_id = p.id
            left join storewise_variants sv on sv.ref_id = pv.ref_id and sv.store_id = v_store_id
		where p.is_active = 1 and sv.is_active = 1 and
		 (CASE
                  WHEN v_sub_category_group_id = 0
                     THEN  
                    (p.sub_category_id = v_sub_category_id  and p.name like concat('%',v_keyword,'%'))
                  ELSE
                    (p.category_id = v_category_id and p.sub_category_id = v_sub_category_id and p.sub_category_group_id = v_sub_category_group_id and p.name like concat('%',v_keyword,'%'))
		 END )
		                     group by pv.id 
        ORDER BY 
        p.created_at desc
		LIMIT v_page, v_size;
	   SELECT FOUND_ROWS() as total;  
end case;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spGetReview` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spGetReview`(IN `p_data` JSON)
BEGIN

	DECLARE v_size INT(4) DEFAULT 10;
	DECLARE v_page INT(4) DEFAULT 0;
    DECLARE v_keyword VARCHAR(255) DEFAULT NULL;
    DECLARE v_type VARCHAR(20) DEFAULT 'admin';
    DECLARE v_sort JSON;
    DECLARE v_field VARCHAR(20) DEFAULT 'created_at';
    DECLARE v_order_by VARCHAR(20) DEFAULT 'desc';
    DECLARE v_product_id int(11) DEFAULT null;
    DECLARE v_store_id int(11) DEFAULT null;
  
   
    SET v_store_id = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.store_id'));
   
   
    SET v_size = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.size')),10);
	SET v_page = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.page')),0) * v_size;
    SET v_keyword = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.keyword')),'');
    SET v_type = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.type')),'admin');
    SET v_sort 	    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort'));
	SET v_field 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort.field')),'created_at');
    SET v_order_by 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort.order_by')),'desc');
    SET v_product_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.product_id'));
 
   
   if length(v_keyword) > 0 then
		set v_size = (select count(id) from review r);
		set v_page = 0;
    end if;

	CASE v_type
		WHEN 'user' THEN
		select SQL_CALC_FOUND_ROWS r.rating ,
		r.id as review_id,r.rating,r.description,r.user_id,r.product_id ,r.order_id ,r.created_at ,r.updated_at, 
		u.first_name ,u.last_name,p.name as product_name,b.name as brand_name, u.user_profile_image from review r
        left join products p ON  p.id = r.product_id
        left join users u ON u.id = r.user_id 
        left join brand b ON b.id = p.brand_id 
        where p.id = v_product_id and (r.rating like concat('%',v_keyword,'%')) 
        ORDER BY 
            CASE v_order_by WHEN 'asc' THEN
             CASE v_field
             WHEN 'created_at' THEN r.created_at 
             WHEN 'rating' THEN r.rating 
             WHEN 'product_name' THEN p.name
             END
            END asc,
           CASE v_order_by WHEN 'desc' THEN
            CASE v_field
            WHEN 'created_at' THEN r.created_at 
            WHEN 'rating' THEN r.rating 
            WHEN 'product_name' THEN p.name
            END
         END desc ,r.created_at desc  
        LIMIT v_page, v_size;
		SELECT FOUND_ROWS() as total;
		
	    when 'admin' then
	    select SQL_CALC_FOUND_ROWS r.rating ,
		r.id as review_id,r.rating,r.description,r.user_id,r.product_id ,r.order_id,o.store_id ,r.created_at ,r.updated_at,
		u.id as user_id,u.first_name,u.last_name,CONCAT(ifnull( u.first_name, ''),' ',ifnull( u.last_name,'')) as name,p.name as product_name,b.name as brand_name from review r
        left join products p ON  p.id = r.product_id
        left join users u ON u.id = r.user_id 
        left join brand b ON b.id = p.brand_id 
        left join orders o ON o.id = r.order_id 
        where
        (CASE 
          WHEN v_store_id IS NOT NULL
          THEN o.store_id =  v_store_id   and   (p.name like concat('%',v_keyword,'%') or u.first_name like concat('%',v_keyword,'%') or u.last_name like concat('%',v_keyword,'%') 
                 or u.id like concat('%',v_keyword,'%') or r.order_id like concat('%',v_keyword,'%')) 
         ELSE 
                p.name like concat('%',v_keyword,'%') or u.first_name like concat('%',v_keyword,'%') or u.last_name like concat('%',v_keyword,'%') 
              or u.id like concat('%',v_keyword,'%') or r.order_id like concat('%',v_keyword,'%')
        END )
        ORDER BY 
        CASE WHEN v_field = 'created_at' AND v_order_by='asc' THEN r.created_at  END ASC,
		CASE WHEN v_field = 'created_at' AND v_order_by='desc' THEN r.created_at  END DESC,
		CASE WHEN v_field = 'rating' AND v_order_by='asc' THEN r.rating  END ASC,
		CASE WHEN v_field = 'rating' AND v_order_by='desc' THEN r.rating END DESC,
		CASE WHEN v_field = 'product_name' AND v_order_by='asc' THEN p.name  END ASC,
		CASE WHEN v_field = 'product_name' AND v_order_by='desc' THEN p.name  END DESC,
        CASE WHEN v_field = 'order_id' AND v_order_by='asc' THEN r.order_id END ASC,
        CASE WHEN v_field = 'order_id' AND v_order_by='desc' THEN r.order_id END DESC,
         r.created_at desc  
        LIMIT v_page, v_size;
		SELECT FOUND_ROWS() as total;
	END CASE;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spGetStockConfig` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spGetStockConfig`(IN `p_data` JSON)
BEGIN

	START TRANSACTION;

    select * from brand_stock_config bsc ;

	COMMIT;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spGetStores` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spGetStores`(IN `p_data` JSON)
BEGIN
-- 	DECLARE v_type VARCHAR(20) DEFAULT 'admin';
	DECLARE v_size INT(4) DEFAULT 10;
	DECLARE v_page INT(4) DEFAULT 0;
    DECLARE v_keyword VARCHAR(255) DEFAULT NULL;
    DECLARE v_filter VARCHAR(255) DEFAULT NULL;
    DECLARE v_sort JSON;
    DECLARE v_field VARCHAR(20) DEFAULT 'created_at';
    DECLARE v_order_by VARCHAR(20) DEFAULT 'desc';
    DECLARE v_start_date datetime DEFAULT NULL;
    DECLARE v_end_date datetime DEFAULT NULL;
	
--     SET v_type 		= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.type')),'admin');
    SET v_keyword 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.keyword')),'');
    SET v_filter 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.filter')),'');
   	SET v_size 		= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.size')),10);
	SET v_page 		= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.page')),0) * v_size ;
    SET v_sort 	    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort'));
	SET v_field 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort.field')),'created_at');
    SET v_order_by 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort.order_by')),'desc');
   
    SET v_start_date= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.start_date'));
	SET v_end_date 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.end_date'));
	
   if length(v_keyword) > 0 then
		set v_size = (select count(id) from store p);
		set v_page = 0;
    end if;

		   select SQL_CALC_FOUND_ROWS s.id as store_id,s.name,s.code from store s;			   
            SELECT FOUND_ROWS() AS total;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spGetSubCategory` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spGetSubCategory`(IN p_data JSON)
BEGIN
	DECLARE v_size INT(4) DEFAULT 10;
	DECLARE v_page INT(4) DEFAULT 0;
    DECLARE v_category_id INT DEFAULT NULL; 
    DECLARE v_keyword VARCHAR(255) DEFAULT NULL;
    DECLARE v_filter VARCHAR(255) DEFAULT NULL;
    DECLARE v_sort JSON;
    DECLARE v_field VARCHAR(20) DEFAULT 'created_at';
    DECLARE v_order_by VARCHAR(20) DEFAULT 'desc';


	SET v_category_id = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.category_id'));
    SET v_size = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.size')),10);
	SET v_page = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.page')),0) * v_size ;

    SET v_keyword 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.keyword')),'');
    SET v_filter 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.filter')),'');
   	SET v_size 		= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.size')),10);
	SET v_page 		= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.page')),0) * v_size ;
    SET v_sort 	    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort'));
	SET v_field 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort.field')),'created_at');
    SET v_order_by 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort.order_by')),'desc');
   
	
	START TRANSACTION;
		SELECT
	    SQL_CALC_FOUND_ROWS sc.id,sc.id as sub_category_id,sc.category_id ,sc.name as sub_category_name,sc.description, c.name as category_name,
	    si.images as sub_category_image,si.id as image_id,
	    sc.created_at ,sc.updated_at
	    FROM sub_category sc 
	    left join category c ON c.id = sc.category_id 
	    left join subcategory_images si ON si.sub_category_id = sc.id 
	    WHERE 
             (CASE 
                  WHEN v_category_id IS NOT NULL
                  THEN 
                    sc.category_id = v_category_id  and (sc.name like CONCAT('%',v_keyword,'%') or c.name like CONCAT('%',v_keyword,'%'))
             ELSE 
             1=1 and (sc.name like CONCAT('%',v_keyword,'%')  or c.name like CONCAT('%',v_keyword,'%'))
             END )
	    and (sc.name LIKE CONCAT('%',v_keyword,'%') OR sc.description  LIKE CONCAT('%',v_keyword,'%'))
	    ORDER BY
		CASE WHEN v_field = 'created_at' AND v_order_by='asc' THEN sc.created_at  END ASC,
		CASE WHEN v_field = 'created_at' AND v_order_by='desc' THEN sc.created_at END DESC,
		CASE WHEN v_field = 'sub_category_name' AND v_order_by='asc' THEN sc.name END ASC,
        CASE WHEN v_field = 'sub_category_name' AND v_order_by='desc' THEN sc.name END DESC,
        sc.created_at desc  
	    LIMIT v_page,v_size ;
	    SELECT FOUND_ROWS() as total;
	COMMIT;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spGetSubCategoryGroup` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spGetSubCategoryGroup`(IN p_data JSON)
BEGIN
	DECLARE v_keyword VARCHAR(255) DEFAULT NULL;
	DECLARE v_size INT(4) DEFAULT 10;
	DECLARE v_page INT(4) DEFAULT 0;
    DECLARE v_sub_category_id INT DEFAULT NULL;
    DECLARE v_type VARCHAR(20) DEFAULT 'admin';
    DECLARE v_check VARCHAR(20) DEFAULT 0 ;
    
	SET v_keyword = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.keyword')),'');
    SET v_sub_category_id = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sub_category_id'));
    SET v_size = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.size')),10);
	SET v_page = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.page')),0) * v_size ;
    SET v_type	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.type')),'admin');
    
	
   CASE v_type
		WHEN 'user' THEN
   
	   SELECT
	    SQL_CALC_FOUND_ROWS sc.id as sub_category_group_id,sc.sub_category_id ,sc.name as sub_category_group_name,sc.description, 
	    sc2.category_id ,sc2.name as sub_category_name, c.name as category_name,p.id as product_id,
	    ifnull(scg.images,"") as sub_category_group_image,scg.id as image_id,
	    sc.created_at ,sc.updated_at
	    FROM sub_category_group sc 
-- 	    left join subcategory_images si ON si.sub_category_id = sc.sub_category_id
	    left join sub_category sc2 on sc2.id = sc.sub_category_id
	    left join category c ON c.id = sc2.category_id 
	    left join sub_category_group_images scg on scg.sub_category_group_id = sc.id
	    inner join products p on p.sub_category_group_id = scg.id 
	    WHERE sc.sub_category_id = v_sub_category_id and p.id != null
-- 	    and p.id IS NOT NULL
	    and (sc.name LIKE CONCAT('%',v_keyword,'%') OR sc.description  LIKE CONCAT('%',v_keyword,'%'))
	    ORDER BY
        sc.created_at desc  
	    LIMIT v_page,v_size ;
	    SELECT FOUND_ROWS() AS total;

     
      WHEN 'admin' THEN
		
		SELECT
	    SQL_CALC_FOUND_ROWS sc.id as sub_category_group_id,sc.sub_category_id ,sc.name as sub_category_group_name,sc.description, 
	    sc2.category_id ,sc2.name as sub_category_name, c.name as category_name,
	    ifnull(scg.images,"") as sub_category_group_image,scg.id as image_id,
	    sc.created_at ,sc.updated_at
	    FROM sub_category_group sc 
-- 	    left join subcategory_images si ON si.sub_category_id = sc.sub_category_id
	    left join sub_category sc2 on sc2.id = sc.sub_category_id
	    left join category c ON c.id = sc2.category_id 
	    left join sub_category_group_images scg on scg.sub_category_group_id = sc.id
-- 	    left join junq_uat.products p on p.sub_category_group_id = scg.id
	    WHERE sc.sub_category_id = v_sub_category_id 
-- 	    and p.id IS NOT NULL
	    and (sc.name LIKE CONCAT('%',v_keyword,'%') OR sc.description  LIKE CONCAT('%',v_keyword,'%'))
	    ORDER BY
        sc.created_at desc  
	    LIMIT v_page,v_size ;
	    SELECT FOUND_ROWS() AS total;
     
     END CASE;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spGetTag` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spGetTag`(IN p_data JSON)
BEGIN
    DECLARE v_keyword VARCHAR(255) DEFAULT NULL;
	DECLARE v_size INT(4) DEFAULT 10;
	DECLARE v_page INT(4) DEFAULT 0;
--     DECLARE v_type VARCHAR(20) DEFAULT 'admin';
    DECLARE v_sort JSON;
    DECLARE v_field VARCHAR(20) DEFAULT 'created_at';
    DECLARE v_order_by VARCHAR(20) DEFAULT 'desc';

	SET v_keyword = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.keyword')),'');
    SET v_size = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.size')),10);
	SET v_page = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.page')),0) * v_size ;
--     SET v_type = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.type')),'admin');
    SET v_sort 	    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort'));
	SET v_field 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort.field')),'created_at');
    SET v_order_by 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort.order_by')),'desc');
	
	START TRANSACTION;
		SELECT
	    SQL_CALC_FOUND_ROWS t.id,t.id as tag_id,t.name as tag_name,t.description,t.created_at,updated_at
        FROM tags t 
	    WHERE t.name LIKE CONCAT('%',v_keyword,'%') OR t.description  LIKE CONCAT('%',v_keyword,'%')
	     ORDER BY
          CASE v_order_by WHEN 'asc' THEN
            CASE v_field
             WHEN 'created_at' THEN t.created_at 
             WHEN 'name' THEN t.name
            END
          END asc,
         CASE v_order_by WHEN 'desc' THEN
           CASE v_field
            WHEN 'created_at' THEN t.created_at
            WHEN 'name' THEN t.name 
           END
         END desc ,
         t.created_at desc  
	    LIMIT v_page,v_size ;
	    SELECT FOUND_ROWS() as total;
	COMMIT;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spGetTopProduct` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spGetTopProduct`(IN `p_data` JSON)
BEGIN
	
	DECLARE v_store_id INT DEFAULT NULL;

    SET v_store_id 		= JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.store_id'));
   
    START TRANSACTION;
   
  CASE 
   WHEN v_store_id IS NOT NULL
   THEN
       
       	select p.id as product_id ,p.name as product_name,od.variant_id,pv.name as variant_name,(sum(od.quantity) * sv.mrp) as total,
	sum(od.quantity) as quantity ,pi.images as image
	from order_details od
        left join orders o on o.id = od.order_id 
        left join product_variant pv on pv.id =  od.variant_id
        left join products p on p.id = pv.product_id 
        left join (select variant_id ,JSON_OBJECT("url",images, "image_id",id) images
                  from product_images
                   group by variant_id
       HAVING max(id)) as pi ON pi.variant_id = od.variant_id
       left join storewise_variants sv on sv.ref_id = pv.ref_id and sv.store_id = o.store_id
       
        where 
        o.status = "order_paid" and YEAR(o.updated_at) =YEAR(CURRENT_TIMESTAMP()) and MONTH(o.updated_at) = MONTH(CURRENT_TIMESTAMP()) and
          o.store_id = v_store_id
        GROUP by od.variant_id
        order by sum(od.quantity) DESC 
        limit 1;
       
   ELSE
       	select p.id as product_id ,p.name as product_name,od.variant_id,pv.name as variant_name,(sum(od.quantity) * pv.mrp) as total,
	sum(od.quantity) as quantity ,pi.images as image
	from order_details od
        left join orders o on o.id = od.order_id 
        left join product_variant pv on pv.id =  od.variant_id
        left join products p on p.id = pv.product_id 
        left join (select variant_id ,JSON_OBJECT("url",images, "image_id",id) images
                  from product_images
                   group by variant_id
       HAVING max(id)) as pi ON pi.variant_id = od.variant_id
        where o.status = "order_paid" and YEAR(o.updated_at) =YEAR(CURRENT_TIMESTAMP()) and MONTH(o.updated_at) = MONTH(CURRENT_TIMESTAMP())
        GROUP by od.variant_id
        order by sum(od.quantity) DESC
        limit 1;
       
    END CASE ;
   
    COMMIT;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spGetUserById` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spGetUserById`(IN p_data JSON)
BEGIN
	DECLARE v_user_id int DEFAULT NOT NULL;

    SET v_user_id = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.user_id'));

    START TRANSACTION;
		SELECT * From users where id=v_user_id;
    COMMIT;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spGetUserRewards` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spGetUserRewards`(in p_data JSON)
BEGIN

    DECLARE v_user_id int DEFAULT not null;

    SET v_user_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.user_id'));

	START TRANSACTION;
    -- Early bird offer
		/* SELECT 
			oo.id AS offer_id, oo.*,
			(SELECT off.original_url FROM offer_image off
				WHERE
					off.offer_id = oo.id) AS image_url
		FROM user_offer uf JOIN
			offers oo ON uf.offer_id = oo.id
		WHERE
			uf.user_id = v_user_id AND oo.tag_id = 1;
          */
          
          SELECT 
	oo.id AS offer_id, oo.*,
			(SELECT off.original_url FROM offer_image off WHERE
					off.offer_id = oo.id) AS image_url,
		EXISTS(select uff.offer_id from user_offer uff where uff.user_id = v_user_id and uff.offer_id = oo.id) as is_active
		FROM offers oo
		WHERE oo.tag_id = 1 order by is_active desc;
        
     -- pending offers
     select *,(select off.original_url from offer_image off where off.offer_id = oo.id) as image_url 
			from offers oo
			where tag_id = 1 and id not in (    
			select offer_id from user_offer
			where user_id = v_user_id); 
    COMMIT;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spGetUsersList` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spGetUsersList`(IN `p_data` JSON)
BEGIN

	DECLARE v_size INT(4) DEFAULT 10;
	DECLARE v_page INT(4) DEFAULT 0;
    DECLARE v_keyword VARCHAR(255) DEFAULT NULL;
    DECLARE v_sort JSON;
    DECLARE v_field VARCHAR(20) DEFAULT 'created_at';
    DECLARE v_order_by VARCHAR(20) DEFAULT 'desc';
    DECLARE v_start_date datetime DEFAULT NULL;
    DECLARE v_end_date datetime DEFAULT NULL;
   
   
    SET v_size = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.size')),10);
	SET v_page = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.page')),0) * v_size;
    SET v_keyword = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.keyword')),'');
    SET v_sort 	    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort'));
	SET v_field 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort.field')),'created_at');
    SET v_order_by 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort.order_by')),'desc');
    SET v_start_date= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.start_date'));
	SET v_end_date 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.end_date'));
	
   
   if length(v_keyword) > 0 then
		set v_size = (select count(id) from users);
		set v_page = 0;
    end if;

	    select SQL_CALC_FOUND_ROWS u.id,
	    u.id as user_id,u.first_name,u.last_name,CONCAT(ifnull( u.first_name, ''),' ',ifnull( u.last_name,'')) as name,u.email,u.mobile,u.gender,
	    u.role_id,u.username,u.user_profile_image,u.firebase_id,u.is_active,u.create_at as created_at from `users` u 
         where u.role_id = 3 and
            (CASE WHEN v_start_date IS NOT NULL
            THEN 
              (DATE(u.create_at) >= v_start_date and DATE(u.create_at) <= v_end_date)       
            ELSE
            (u.first_name like CONCAT('%',v_keyword,'%') or u.last_name like CONCAT('%',v_keyword,'%') or u.mobile like CONCAT('%',v_keyword,'%'))
            END)
        ORDER BY 
            CASE v_order_by WHEN 'asc' THEN
             CASE v_field
             WHEN 'created_at' THEN u.create_at 
             WHEN 'first_name' THEN u.first_name
             WHEN 'gender' THEN u.gender
             WHEN 'is_active' THEN u.is_active
             END
            END asc,
           CASE v_order_by WHEN 'desc' THEN
            CASE v_field
            WHEN 'created_at' THEN u.create_at 
            WHEN 'first_name' THEN u.first_name
            WHEN 'gender' THEN u.gender
            WHEN 'is_active' THEN u.is_active
            END
         END desc ,
         u.create_at desc  
        LIMIT v_page, v_size;
		SELECT FOUND_ROWS() as total;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spGetVariant` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spGetVariant`(IN p_data JSON)
BEGIN
	DECLARE v_product_id INT DEFAULT NULL;
	DECLARE v_keyword VARCHAR(255) DEFAULT NULL;
    DECLARE v_filter VARCHAR(255) DEFAULT NULL;
    DECLARE v_size INT DEFAULT 10;
	DECLARE v_page INT DEFAULT 0;
    DECLARE v_sort JSON;
    DECLARE v_field VARCHAR(20) DEFAULT 'created_at';
    DECLARE v_order_by VARCHAR(20) DEFAULT 'desc';
    DECLARE v_store_id INT(11) DEFAULT NULL;

    SET v_product_id= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.product_id'));
    SET v_filter 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.filter')); 
    SET v_keyword 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.keyword')),'');
    SET v_size 		= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.size')),10);
	SET v_page 		= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.page')),0) * v_size ;
    SET v_sort 	    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort'));
    SET v_field 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort.field')),'created_at');
    SET v_order_by 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort.order_by')),'desc');
    SET v_store_id      = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.store_id'));

	if length(v_keyword) > 0 then
		set v_size = (select count(id) from product_variant);
		set v_page = 0;
    end if;
   	 
		
SELECT SQL_CALC_FOUND_ROWS pv.id as variant_id,p.id as product_id, p.name as product_name, pi.images,
    pv.name as variant_name,pv.sku as variant_sku,ifnull(sv.price,0) as price,ifnull(sv.cost_price,0) as cost_price,ifnull(sv.mrp,0) as mrp,
    pv.weight,ifnull(sv.stock,0) as variant_stock,pv.is_active as variant_is_active,ifnull(sv.is_active,0) as store_is_active,pv.created_at,
    case
    when ifnull(sv.stock,0) >= bsc.out_of_stock
    AND ifnull(sv.stock,0) < bsc.finishing_soon
    THEN 'out of stock'
    when ifnull(sv.stock,0) < bsc.in_stock 
    AND ifnull(sv.stock,0) >= bsc.finishing_soon
    then 'finishing soon' 
    when ifnull(sv.stock,0) < bsc.plenty
    AND ifnull(sv.stock,0) >= bsc.in_stock 
    THEN 'in stock'
    when  ifnull(sv.stock,0) >= bsc.plenty 
    then 'plenty' 
    else 'out of stock'  
    end  as variant_status
    FROM product_variant pv
    left JOIN    products p ON  p.id = pv.product_id
	left join brand_stock_config bsc on bsc.brand_id  = p.brand_id
-- 	left join product_images pi on pi.variant_id = pv.id
	 left join
                      (select variant_id ,
--                       JSON_ARRAYAGG(JSON_OBJECT("url",images, "image_id",id))
                      CAST(CONCAT(
                      '[', 
                      GROUP_CONCAT(JSON_OBJECT("url",images, "image_id",id)),
                      ']'
                       ) as JSON) 
                      images
                      from product_images
                      group by variant_id
                      ) as pi ON pi.variant_id = pv.id
    left join storewise_variants sv on sv.ref_id = pv.ref_id and sv.store_id = v_store_id
	where
--  (p.name like concat('%',v_keyword,'%') or pv.name like concat('%',v_keyword,'%') or pv.sku like concat('%',v_keyword,'%'))
-- 	 (CASE 
--         WHEN v_store_id IS NOT NULL
--         THEN
--          sv.store_id =  v_store_id 
--          ELSE 1=1
--      END) and
	(CASE
    WHEN v_product_id is not NULL THEN p.id = v_product_id
    WHEN v_filter is not NULL THEN 
	 (case v_filter
    when  'out of stock'
    THEN 
    ifnull(sv.stock,0) >= bsc.out_of_stock 
    AND ifnull(sv.stock,0) < bsc.finishing_soon
    when 'finishing soon' 
    then 
    ifnull(sv.stock,0) < bsc.in_stock 
    AND ifnull(sv.stock,0) >= bsc.finishing_soon
    when 'in stock'
    then
    ifnull(sv.stock,0) < bsc.plenty
    AND ifnull(sv.stock,0) >= bsc.in_stock
    when 'plenty'
    then
     ifnull(sv.stock,0) >= bsc.plenty 
    else 
     ifnull(sv.stock,0) >= 0 
    END) 
     ELSE
     (p.name like concat('%',v_keyword,'%') or pv.name like concat('%',v_keyword,'%') or pv.sku like concat('%',v_keyword,'%'))
END)
        ORDER BY 
        CASE WHEN v_field = 'id' AND v_order_by='asc' THEN pv.id  END ASC,
		CASE WHEN v_field = 'id' AND v_order_by='desc' THEN pv.id END DESC,
        CASE WHEN v_field = 'created_at' AND v_order_by='asc' THEN pv.created_at  END ASC,
		CASE WHEN v_field = 'created_at' AND v_order_by='desc' THEN pv.created_at END DESC,
		CASE WHEN v_field = 'variant_stock' AND v_order_by='asc' THEN pv.stock END ASC,
		CASE WHEN v_field = 'variant_stock' AND v_order_by='desc' THEN pv.stock END DESC,
        CASE WHEN v_field = 'variant_status' AND v_order_by='asc' THEN variant_status END ASC,
        CASE WHEN v_field = 'variant_status' AND v_order_by='desc' THEN variant_status END DESC,
        CASE WHEN v_field = 'variant_is_active' AND v_order_by='asc' THEN pv.is_active END ASC,
        CASE WHEN v_field = 'variant_is_active' AND v_order_by='desc' THEN pv.is_active END DESC,
        pv.id desc
		LIMIT v_page, v_size;
	
	   SELECT FOUND_ROWS() as total;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spGetWishList` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spGetWishList`(IN p_data JSON)
BEGIN

	DECLARE v_user_id INT DEFAULT NOT NULL;
	DECLARE v_size INT DEFAULT 10;
	DECLARE v_page INT DEFAULT 0;
    DECLARE v_store_id INT(11) DEFAULT NULL;

	SET v_user_id = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.user_id'));
	SET v_size = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.size')),10);
	SET v_page = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.page')),0) * v_size ;
    SET v_store_id      = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.store_id')),7);
	
    START TRANSACTION;
		select SQL_CALC_FOUND_ROWS w.id as wishlist_id, w.created_at, p.name, p.description,w.product_id ,w.variant_id,sv.stock,pv.weight,
		format(sv.price,2) as price,format(sv.mrp,2) as mrp,format(sv.cost_price,2) as cost_price,
		ifnull(pi.images,JSON_OBJECT("url",'',"image_id",null)) as image
		from wishlist w
		left join products p on w.product_id = p.id
		left join product_variant pv on pv.id = w.variant_id
		left join storewise_variants sv on sv.ref_id = pv.ref_id 
		left join (select variant_id ,JSON_OBJECT("url",images, "image_id",id) images
        from product_images
        group by variant_id
        HAVING max(id)) as pi ON pi.variant_id = w.variant_id 
		where w.user_id = v_user_id and sv.store_id = v_store_id
        ORDER BY w.id
	    LIMIT v_page, v_size;
        SELECT FOUND_ROWS() as total;
    COMMIT;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spInitiateInvoice` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spInitiateInvoice`(IN p_data JSON)
BEGIN
	DECLARE v_order_id int default not null;
    
    SET v_order_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.order_id'));
    
    START TRANSACTION;
    
    select o.id as order_id,concat(ifnull(u.first_name,'Unknown'),' ', ifnull(u.last_name,'User')) as 'full_name',
		u.mobile, u.email, o.*, ua.*,
        (select JSON_ARRAYAGG(JSON_OBJECT("name", pv.name, "amount", CAST(pv.price * 100 AS DECIMAL(0)), "currency", "INR", "quantity", od.quantity))
			 from product_variant pv
			 join order_details od on pv.id = od.variant_id
			 where od.order_id = v_order_id ) as line_items 
			 from orders o 
		join user_address ua on o.address_id = ua.id
		left join users u on o.user_id = u.id
		where o.id = v_order_id;
    
    COMMIT;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spInitiatePaymentByInvoice` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spInitiatePaymentByInvoice`(IN p_data JSON)
BEGIN
	DECLARE v_ref_id varchar (220) default not null;
	
    DECLARE v_user_id int  default not null;
	DECLARE v_order_id int  default not null;
    
	DECLARE v_grand_total DOUBLE default not null;
    
	DECLARE v_amount DOUBLE default not null;
	DECLARE v_amount_paid DOUBLE default 0;
	DECLARE v_amount_due DOUBLE default not null;
	
    DECLARE v_currency varchar(100) default not null;
	DECLARE v_status varchar(100) default not null;
	DECLARE v_invoice_url varchar(255) default null;
	
    DECLARE v_attempts int default not null;
	DECLARE v_created_at date default current_timestamp();
    
	DECLARE v_last_payment_id int default null;
   
	SET v_ref_id 		= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.order_id'));
	
    -- custom payload
    SET v_order_id 		= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.junq_order_id'));
	SET v_user_id 		= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.junq_user_id'));
	SET v_grand_total 		= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.grand_total'));
    
	SET v_amount 		= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.amount'));
	SET v_amount_paid 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.amount_paid'));
	SET v_amount_due 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.amount_due'));
	
    #SET v_currency 		= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.currency'));
	SET v_status 		= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.status'));
	SET v_invoice_url 		= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.short_url'));
    
	#SET v_attempts 		= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.attempts'));
	#SET v_created_at 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.created_at'));
    
   START TRANSACTION;
   
	INSERT INTO `payment`(`user_id`,`provider`,`order_id`,`amount`,`reference_id`,`status`,`invoice_url`)
				VALUES ( v_user_id, 'Razor Pay', v_order_id, v_grand_total/100, v_ref_id, v_status, v_invoice_url);
	
    SET v_last_payment_id	= LAST_INSERT_ID();	
    
    INSERT INTO `payment_log`(`payment_id`,`reference_id`,`status`)
				VALUES (v_last_payment_id, v_ref_id, v_status);
    
    update orders set payment_id = v_last_payment_id
    where id = 	v_order_id;
	
    SELECT 1 AS status, 'Payment Initiated successfully' AS msg, v_last_payment_id AS payment_id;
    
   COMMIT;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spOfferByUserID` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spOfferByUserID`(in p_data json)
BEGIN

    declare v_user_id int DEFAULT null;
    declare v_check	 int DEFAULT null;

    SET v_user_id = JSON_UNQUOTE(json_extract(p_data, '$.user_id'));

	SET v_check =	(select count(*) from user_offer where user_id =v_user_id and
		created_at > DATE_SUB(current_timestamp(), INTERVAL 24 HOUR));

    START TRANSACTION;

    case when v_check > 0 
    
    then 
		(select 0 status_check);
	else 
    (select 1 as status_check, oo.* from offers oo
		where oo.tag_id = 1 and id not in (select offer_id from user_offer uf where uf.user_id =v_user_id)
		order by rand()
		limit 1);
    end case;
    COMMIT;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spOpenOfferByUser` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spOpenOfferByUser`(in p_data json)
BEGIN
	DECLARE v_offer_id int default null;
	DECLARE v_user_id int default null;
	DECLARE v_check int default null;

    SET v_offer_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.offer_id'));

    SET v_user_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.user_id'));

	SET v_check	= (select count(*) from user_offer where offer_id = v_offer_id and user_id = v_user_id);

    Start transaction;
    if v_check = 0
		then
			insert into user_offer(offer_id, user_id) values(v_offer_id, v_user_id);
			SELECT 1 status, 'offer opened successfully' AS msg;
		else
			select 0 status, 'duplicate request' as msg;
    end if;
    commit;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spOrderByID` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spOrderByID`(IN p_data JSON)
BEGIN
	#call spOrderByID('{"order_id":4}');
    DECLARE v_order_id INT DEFAULT NOT NULL;
    DECLARE v_type VARCHAR(15) DEFAULT NOT NULL;
    DECLARE v_store_id INT(11) DEFAULT NULL;
    

    SET v_order_id = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.order_id'));
    SET v_type = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.type')); 
    SET v_store_id      = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.store_id')),7);
     
   START TRANSACTION;
	 select o.id as order_id , o.status  as order_status,o.order_type, o.created_at,o.address_id,u.first_name,u.last_name,u.mobile,
	 o.store_id,s.name as store_name,s.code as store_code,
	 concat(ifnull(u.first_name,''),' ', ifnull(u.last_name,'')) as name,
	 b.name as brand_name, b.id as brand_id,p.id as payment_id, p.status as payment_status,p.invoice_url,p.reference_id ,p.razorpay_ref_id , o.created_at,
-- 	 REPLACE(FORMAT(o.sub_total ,2),',','') as sub_total, REPLACE(FORMAT(o.gst,2),',','') as gst, REPLACE(FORMAT(o.discount ,2),',','') as discount , REPLACE(FORMAT(o.grand_total,2),',','') as grand_total,format(d.delivery_cost,2) as delivery_cost,
	 REPLACE(FORMAT(o.sub_total ,2),',','') as sub_total, REPLACE(FORMAT(o.gst,2),',','') as gst, REPLACE(FORMAT(o.discount ,2),',','') as discount , REPLACE(FORMAT(o.grand_total,2),',','') as grand_total,
	 REPLACE(FORMAT(d.delivery_cost ,2),',','') as delivery_cost,
	 JSON_OBJECT("name", ua.name,"address_type",ua.address_type,"phone",ua.phone,"address1",ua.address1,"address2",ua.address2,"city",ua.city,"state",ua.state,
	 "pincode",ua.pincode,"latitude",CAST(ua.latitude as CHAR(20)),"longitude",CAST(ua.longitude as CHAR(20))) delivery_address ,
    # (select ifnull(avg(rr.rating),0) from review rr where rr.product_id = p2.id) as rate,
     (select JSON_ARRAYAGG(JSON_OBJECT("order_details_id", od.id, "product_id",od.product_id,"variant_id",od.variant_id,"product_name",p2.name,"variant_name",pv.name,"quantity", od.quantity, "price", 
     REPLACE(FORMAT(sv.price,2),',',''),"mrp",REPLACE(FORMAT(sv.mrp,2),',',''),"buying_price", REPLACE(FORMAT(od.buying_price,2),',',''),"image",ifnull(pi.images,JSON_OBJECT("url",'',"ïmage_id",null))))
     from order_details od 
     left join products p2 ON od.product_id = p2.id 
     left join product_variant pv on pv.id = od.variant_id
     left join orders o2 on o2.id = od.order_id 
     left join storewise_variants sv on sv.ref_id = pv.ref_id and sv.store_id = o2.store_id
     left join (select product_id ,JSON_OBJECT("url",images, "image_id",id) images
     from product_images
     group by product_id
     HAVING max(id)) as pi ON pi.product_id = p2.id 
	 where od.order_id = v_order_id ) as OrderDetails,
	(select JSON_ARRAYAGG(JSON_OBJECT("order_log_id",ol.id,"status",ol.status,"created_at",DATE_FORMAT(ol.created_at,'%Y-%m-%dT%H:%i:%sZ'),"reason",ifnull(ol.reason,''),"reference_id",ol.reference_id, "meta",ifnull(ol.meta,JSON_OBJECT("empty",true)))) 
	from order_log ol  
	where ol.id in (select max(id) from order_log ol2 where ol2.order_id = v_order_id group by ol2.status )) as OrderLogs,
	JSON_OBJECT("id",d.id,"sfx_id",d.SFX_order_id,"track_url",d.track_url,"status",d.status,"delivery_cost",format(d.delivery_cost,2)) as delivery
	 from orders o 			   
			left join	users u on o.user_id = u.id 
			left join	brand b on o.brand_id = b.id
			left join	payment p on o.payment_id = p.id
			left join user_address ua on ua.id = o.address_id
			left join delivery d on d.order_id = o.id
			left join store s on s.id = o.store_id
             where
            (CASE v_type
            WHEN 'user' THEN 
             o.id = v_order_id and o.status != 'order_created'
            else  o.id = v_order_id
            END);
  
  
  
  
  
  
  
  

           
           
  
  
--             
		SELECT FOUND_ROWS() as total;
   COMMIT;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spOrderList` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spOrderList`(IN `p_data` JSON)
BEGIN

	# call spOrderList('{"brand_id": "3", "type":"brand"}');

 

	DECLARE v_type VARCHAR(20) DEFAULT 'admin';

	DECLARE v_user_id int(11) DEFAULT null;

    DECLARE v_store_id int(11) DEFAULT null;

    DECLARE v_store_code VARCHAR(20) DEFAULT null;

    DECLARE v_size INT(4) DEFAULT 10;

	DECLARE v_page INT(4) DEFAULT 0;

    DECLARE v_keyword VARCHAR(255) DEFAULT NULL;

    DECLARE v_sort JSON;

    DECLARE v_field VARCHAR(20) DEFAULT 'created_at';

    DECLARE v_order_by VARCHAR(20) DEFAULT 'desc';

   

    DECLARE v_filter VARCHAR(255) DEFAULT NULL;

    

    DECLARE v_start_date datetime DEFAULT NULL;

    DECLARE v_end_date datetime DEFAULT NULL;

   

    DECLARE v_order_id int(11) DEFAULT null;

   

    SET v_type 		= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.type')),'admin');

    SET v_user_id 	= JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.user_id'));

--     SET v_store_id 	= JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.store_id'));

    SET v_store_code 	= JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.store_code'));

    SET v_keyword 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.keyword')),'');

   	SET v_size 		= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.size')),10);

	SET v_page 		= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.page')),0) * v_size ;

    SET v_sort 	    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort'));

	SET v_field 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort.field')),'created_at');

    SET v_order_by 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sort.order_by')),'desc');

   

    SET v_filter 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.filter')),'');

   

    SET v_start_date= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.start_date'));

	SET v_end_date 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.end_date'));

    

    SET v_order_id 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.order_id'));

    

    SET v_store_id = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.store_id'));

   

   if length(v_keyword) > 0 then

		set v_size = (select count(id) from orders o);

		set v_page = 0;

    end if;

   

	CASE v_type

		WHEN 'user' THEN

-- 		   select SQL_CALC_FOUND_ROWS o.id, o.id as order_id, o.status  as order_status,o.offer_id, o.created_at,o.order_type, u.first_name as name,u.mobile,

-- 		   b.name as brand_name, p.status as payment_status,p.invoice_url,o.grand_total 

--            ,ua.address_type,

--            (select JSON_ARRAYAGG(JSON_OBJECT("order_details_id", od.id,

--            "product_id",od.product_id,"variant_id",od.variant_id,"product_name",p2.name,"quantity", od.quantity, "price", REPLACE(FORMAT(sv.price,2),',',''), "cost_price", REPLACE(FORMAT(sv.cost_price,2),',',''),

--            "mrp", REPLACE(FORMAT(sv.mrp,2),',',''),"buying_price", REPLACE(FORMAT(od.buying_price,2),',',''),"image",ifnull(pi.images,JSON_OBJECT("url",'',"ïmage_id",null))))

--             from order_details od 

--             left join products p2 ON od.product_id = p2.id

--             left join product_variant pv on pv.id = od.variant_id

--             left join storewise_variants sv on sv.ref_id = pv.ref_id and sv.store_id = (select o2.store_id from orders o2 where o2.id = od.order_id ) 

--             left join (select product_id ,JSON_OBJECT("url",images, "image_id",id) images

--             from product_images

--             group by product_id

--             HAVING max(id)) as pi ON pi.product_id = p2.id 

--             where od.order_id = o.id ) as OrderDetails,

--             JSON_OBJECT("id",d.id,"sfx_id",d.SFX_order_id,"track_url",d.track_url,"status",d.status) as delivery

-- 			from orders o 			   

--             left join	users u on o.user_id = u.id 

-- 			left join	brand b on o.brand_id = b.id

-- 			left join	payment p on o.payment_id = p.id

-- 			left join user_address ua on ua.id = o.address_id 

-- 			left join delivery d on d.order_id = o.id

--             where o.user_id = v_user_id and u.first_name like CONCAT('%',v_keyword,'%') and o.status != 'order_created'

--             ORDER BY o.created_at desc

--             LIMIT v_page, v_size;
		select   o.id as order_id, o.status  as order_status,o.offer_id, o.created_at,o.order_type, u.first_name as name,u.mobile,
		   b.name as brand_name, p.status as payment_status,p.invoice_url,o.grand_total 
           ,ua.address_type,
           (select JSON_ARRAYAGG(JSON_OBJECT("order_details_id", od.id,
           "product_id",od.product_id,"variant_id",od.variant_id,"product_name",p2.name,"quantity", od.quantity, "price", REPLACE(FORMAT(sv.price,2),',',''), "cost_price", REPLACE(FORMAT(sv.cost_price,2),',',''),
           "mrp", REPLACE(FORMAT(sv.mrp,2),',',''),"buying_price", REPLACE(FORMAT(od.buying_price,2),',',''),"image",ifnull(pi.images,JSON_OBJECT("url",'',"ïmage_id",null))))
            from order_details od 
            left join products p2 ON od.product_id = p2.id
            left join product_variant pv on pv.id = od.variant_id
            left join storewise_variants sv on sv.ref_id = pv.ref_id and sv.store_id = (select o2.store_id from orders o2 where o2.id = od.order_id ) 
            left join (select product_id ,JSON_OBJECT("url",images, "image_id",id) images
            from product_images
            group by product_id
            HAVING max(id)) as pi ON pi.product_id = p2.id 
            where od.order_id = o.id ) as OrderDetails,
            JSON_OBJECT("id",d.id,"sfx_id",d.SFX_order_id,"track_url",d.track_url,"status",d.status) as delivery
			from orders o 			   
            left join	users u on o.user_id = u.id 
			left join	brand b on o.brand_id = b.id
			left join	payment p on o.payment_id = p.id
			left join user_address ua on ua.id = o.address_id 
			left join delivery d on d.order_id = o.id
            where o.user_id = v_user_id and o.status != 'order_created'
            ORDER BY o.created_at desc;

        SELECT FOUND_ROWS() AS total;

       

		when 'admin' then

			select SQL_CALC_FOUND_ROWS o.id, o.id as order_id, o.status  as order_status,o.store_id,s.name as store_name,s.code as store_code, o.created_at,o.order_type, u.first_name,u.last_name,u.mobile,

			CONCAT(ifnull( u.first_name, ''),' ',ifnull( u.last_name,'')) as name,

			u.mobile, u.user_profile_image, p.status as payment_status,p.invoice_url,

			o.grand_total

			from orders o 			   

			left join	users u on o.user_id = u.id 

			left join	brand b on o.brand_id = b.id

			left join	payment p on o.payment_id = p.id 

			left join store s on s.id = o.store_id

		    where

             (CASE 

                  WHEN v_store_id IS NOT NULL

                  THEN 

                    (CASE 

                    WHEN v_start_date IS NOT NULL

                    THEN 

                     (DATE(o.created_at) >= v_start_date and DATE(o.created_at) <= v_end_date)

                    ELSE

                     u.first_name like CONCAT('%',v_keyword,'%') or u.last_name like concat('%',v_keyword,'%') or o.id like concat('%',v_keyword,'%') or u.mobile like concat('%',v_keyword,'%')

		          END ) and   o.store_id =  v_store_id 

             ELSE 

                   (CASE 

                    WHEN v_start_date IS NOT NULL

                    THEN 

                     (DATE(o.created_at) >= v_start_date and DATE(o.created_at) <= v_end_date)

                    ELSE

                     u.first_name like CONCAT('%',v_keyword,'%') or u.last_name like concat('%',v_keyword,'%') or o.id like concat('%',v_keyword,'%') or u.mobile like concat('%',v_keyword,'%') 

		          END )

              END )

           having 

-- 		   o.status like CONCAT('%',v_filter,'%')   

		   (CASE 

               WHEN v_filter = 'COD'

                THEN 

                o.order_type like CONCAT('%',v_filter,'%') 

                ELSE

                o.status like CONCAT('%',v_filter,'%')

           END )

		ORDER BY

		CASE WHEN v_field = 'order_id' AND v_order_by='asc' THEN o.id  END ASC,

		CASE WHEN v_field = 'order_id' AND v_order_by='desc' THEN o.id END DESC,

        CASE WHEN v_field = 'created_at' AND v_order_by='asc' THEN p.created_at  END ASC,

		CASE WHEN v_field = 'created_at' AND v_order_by='desc' THEN p.created_at END DESC,

		CASE WHEN v_field = 'order_status' AND v_order_by='asc' THEN o.status  END ASC,

		CASE WHEN v_field = 'order_status' AND v_order_by='desc' THEN o.status END DESC,

		CASE WHEN v_field = 'payment_status' AND v_order_by='asc' THEN p.status  END ASC,

		CASE WHEN v_field = 'payment_status' AND v_order_by='desc' THEN p.status  END DESC,

        CASE WHEN v_field = 'grand_total' AND v_order_by='asc' THEN o.grand_total END ASC,

        CASE WHEN v_field = 'grand_total' AND v_order_by='desc' THEN o.grand_total END DESC,

        o.created_at desc

       LIMIT v_page, v_size;

      SELECT FOUND_ROWS() AS total;

     

     WHEN 'staff' THEN

     SET v_store_id = (SELECT id from store where code = v_store_code);

		   select SQL_CALC_FOUND_ROWS o.id, o.id as order_id, o.status  as order_status, o.created_at,o.order_type,

           (select JSON_ARRAYAGG(JSON_OBJECT("order_details_id", od.id, "product_id",od.product_id,"variant_id",od.variant_id,"product_name",CONCAT(p2.name,' - ',pv.name),

           "quantity", od.quantity, "variant_name",pv.name,"product_sku",p2.sku,"variant_sku",pv.sku))

            from order_details od 

            left join products p2 ON od.product_id = p2.id

            left join product_variant pv on pv.id = od.variant_id

            where od.order_id = o.id ) as packageDetails

            from orders o 			   

            where 

             (CASE 

                 WHEN v_order_id IS NOT NULL

                 THEN 

                o.id = v_order_id and o.store_id = v_store_id and o.status IN('order_unpaid','order_paid')

                ELSE

                o.store_id = v_store_id and o.status IN('order_unpaid','order_paid')

--                 ,'partially_packed','packed','updated_packed'

                END )

                HAVING o.status like CONCAT('%',v_filter,'%')

            ORDER BY o.id desc

            LIMIT v_page, v_size;

        SELECT FOUND_ROWS() AS total;

	END CASE;

 

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spOrderStatus` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spOrderStatus`(IN `p_data` JSON)
BEGIN

	

	DECLARE v_order_id INT(11) DEFAULT NULL;

    DECLARE v_user_id INT(11) DEFAULT NULL;

    DECLARE v_grand_total decimal(10,2) DEFAULT NULL;

    DECLARE v_store_id int(11) DEFAULT null;

   



	DECLARE v_updated_by INT(11) DEFAULT NULL;

    DECLARE v_last_payment_id INT(11) DEFAULT NULL;

    

    DECLARE v_reason VARCHAR(128) DEFAULT NULL;



	SET v_order_id    = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.order_id'));

    SET v_updated_by  = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.updated_by'));

    SET v_reason      = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.reason'));

--     SET v_store_id = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.store_id')),7);



	START TRANSACTION;

    SET v_store_id    = (select o.store_id from orders o where o.id = v_order_id );

	

		UPDATE orders o

		SET o.status     = "order_paid",

		    o.store_id   = v_store_id,

			o.updated_at = NOW()

		WHERE

			o.id 		 = v_order_id;

 

		 SET v_user_id    = (select user_id from orders o where o.id = v_order_id );

		 SET v_grand_total    = (select grand_total from orders o where o.id = v_order_id );

 		

        INSERT INTO `payment`(`user_id`,`order_id`,`amount`,`status`,payment_type)

	    VALUES ( v_user_id, v_order_id, v_grand_total, 'captured','COD');

	   

	

        SET v_last_payment_id	= LAST_INSERT_ID();	

       

       insert into order_log(order_id,status,reference_id,updated_by) 

       values(v_order_id,"order_paid",CONCAT('JF_','pay_',v_last_payment_id),v_updated_by);



       

       update payment p set p.razorpay_ref_id = CONCAT('JF_','pay_',v_last_payment_id) where p.id = v_last_payment_id;

       update orders o set o.payment_id  = v_last_payment_id where o.id =  v_order_id;

       

    

        INSERT INTO `payment_log`(`payment_id`,`status`)

	    VALUES (v_last_payment_id, 'captured');

	   

	   

	    SELECT 1 as status, 'Updated order status successfully' as msg;

       



	COMMIT;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spOrderStatusCallback` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spOrderStatusCallback`(in p_data json)
BEGIN



	declare v_meta json;

    declare v_order_id int default null;

    declare v_status varchar(20) default null;

    DECLARE v_store_id int(11) DEFAULT null;

    

    SET v_meta = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$'));

    SET v_order_id = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.client_order_id'));

    SET v_status = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.order_status'));

--     SET v_store_id = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.store_id')),7);

    

    

    START TRANSACTION;

--        if p_data.secret IS NOT NULL

--        THEN

-- 		insert into order_log (order_id, status)

-- 						values (v_order_id, v_status);

--        else

   SET v_store_id    = (select o.store_id from orders o where o.id = v_order_id );

	

       insert into order_log (order_id, meta, status)

						values (v_order_id, v_meta, v_status);

-- 		end if;

		

	    update orders

	    set status = v_status,

	        store_id = v_store_id,

	        updated_at = NOW()

		where id = v_order_id;

				

		select 1 as status, 'msg' as 'successfully captured';

        

    COMMIT;

    



END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spPaymentByID` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spPaymentByID`(IN `p_data` JSON)
BEGIN



    DECLARE v_payment_id int(20) DEFAULT NOT NULL;

    DECLARE v_store_id int(11) DEFAULT null;

   

    SET v_payment_id = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.payment_id'));

    SET v_store_id = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.store_id')),7);

   

		   select  p.id as payment_id,u.first_name,u.last_name,CONCAT(ifnull( u.first_name, ''),' ',ifnull( u.last_name,'')) as name ,

		   u.mobile ,u.email,

		   p.user_id ,p.payment_type ,p.provider ,p.order_id,

		   o.store_id,s.name as store_name,s.code as store_code,

           p.amount ,p.reference_id ,p.razorpay_ref_id ,p.status as payment_status,p.invoice_url,p.meta ,p.created_at,

           (SELECT CAST(CONCAT(

            '[', 

           GROUP_CONCAT(JSON_OBJECT("payment_log_id",pl.id,"status",pl.status,"reference_id",pl.reference_id,"created_at",pl.created_at)),

           ']'

             ) as JSON)  from payment_log pl where pl.payment_id = p.id)

	        as PaymentLogs

			from payment p 			   

			left join	`users` u on p.user_id = u.id 

			left join orders o on o.payment_id = p.id 

			left join store s on s.id = o.store_id

            where p.id = v_payment_id;



END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spPlaceDeliveryOrder` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spPlaceDeliveryOrder`(IN p_data json)
BEGIN

	

    declare v_order_id int default not null;

    

    SET v_order_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.order_id'));

    

	START TRANSACTION;

    

    -- Generate OTP

    

    update orders

    set pickup_otp =(select floor(0+ RAND() * 10000)), return_otp =(select floor(0+ RAND() * 10000))

    where id = v_order_id;

    

    

    select o.id as order_id, o.grand_total, o.pickup_otp, o.return_otp, (select contact_no from store where id = o.store_id) as pickup_contact_number,

 (select JSON_ARRAYAGG(JSON_OBJECT("id", pv.id, "weight",CAST(format(pv.weight/1000,2) as DECIMAL(10,2)),"quantity",od.quantity, "name",pv.name , "price",pv.price))

    from product_variant pv

    join order_details od on pv.id = od.variant_id

    where od.order_id = v_order_id

    ) as product_details, (select JSON_OBJECT("address_line_1", ua.address1, "city",ua.city,"contact_number",ua.phone, "address_line_2",ua.address2 , "name",ua.name, "latitude",ua.latitude,"longitude",ua.longitude)

    from user_address ua

    where ua.id = o.address_id

    ) as customer_details 

    from orders o

 where o.id = v_order_id;

    

    

    COMMIT;





END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spSendOtp` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spSendOtp`(IN p_data JSON)
BEGIN
DECLARE v_temp_id INT(11) DEFAULT NOT NULL;
	DECLARE v_user_id INT(11) DEFAULT NOT NULL;
    DECLARE v_customer_id INT(11) DEFAULT NOT NULL;
    DECLARE v_expert_id INT(11) DEFAULT NOT NULL;
    DECLARE v_brand_id INT(11) DEFAULT NOT NULL;
    DECLARE v_centre_id INT(11) DEFAULT NOT NULL;
    DECLARE v_role_id INT(11) DEFAULT NOT NULL;
    DECLARE v_admin_type_id INT(11) DEFAULT NOT NULL;
    DECLARE v_first_name varchar(50) DEFAULT NULL;
    DECLARE v_username varchar(50) DEFAULT NULL;
    DECLARE v_email varchar(50) DEFAULT NULL;
    DECLARE v_mobile varchar(50) DEFAULT NULL;
    DECLARE v_password varchar(100) DEFAULT NULL;
    DECLARE v_category_ids JSON;
    declare v_training_type varchar(50) default null;
    DECLARE v_invited_by INT(11) DEFAULT NOT NULL;
    DECLARE v_sent_otp varchar(100) DEFAULT NOT NULL;
    DECLARE v_shortcode varchar(100) DEFAULT NOT NULL;
    DECLARE v_type varchar(10) DEFAULT NOT NULL;
    DECLARE v_invite_count int(11) DEFAULT NOT NULL;
    DECLARE v_counselling int(11) DEFAULT NOT NULL;
   
   
     DECLARE i  INT DEFAULT 1;
    
    SET v_user_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.user_id'));
    SET v_role_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.role_id'));
    SET v_admin_type_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.admin_type_id'));
    SET v_first_name = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.name')),'Rapsap User');
    SET v_email = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.email'));
    SET v_mobile = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.mobile'));
    SET v_password = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.password'));
    SET v_category_ids = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.category_ids'));
    SET v_training_type = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.training_type'));
    SET v_invited_by = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.invited_by'));
    SET v_username = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.username'));
    SET v_sent_otp = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.otp'));
    SET v_shortcode = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.shortcode'));
    SET v_type = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.type'));
    
    
   
     SET v_temp_id = (select t.id from temp_user t where t.mobile = v_mobile limit 1);
     SET v_user_id = (select u.id from users u where u.mobile = v_mobile limit 1); 

   
--   for temp user in sent otp
 	 IF v_temp_id IS NULL
 	 THEN
  -- fresh user
   		insert into temp_user (first_name,mobile,sent_otp)
   		values(v_first_name,v_mobile,v_sent_otp);
  	 	SET v_temp_id = LAST_INSERT_ID();
  -- user already got otp once, but not verified
     ELSEIF v_temp_id IS NOT NULL and v_user_id IS NULL
    	 THEN
   			update temp_user t set t.sent_otp = v_sent_otp where t.mobile = v_mobile;
  -- user verification done once
  	 ELSEIF v_temp_id IS NOT NULL and v_user_id IS NOT NULL
  		THEN   
   			 update temp_user t set t.sent_otp = v_sent_otp,
    		t.first_name = (select u.first_name  from users u where u.id = v_user_id)
   			 where t.mobile = v_mobile;
  
 	 END IF;
 
		SELECT 1 AS flag, 'User Created Successfully!' AS msg, v_sent_otp as otp, 
	    if(v_type IS NOT NULL, v_temp_id,v_user_id) as temp_id ;
       
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spStoreMapping` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spStoreMapping`(IN `p_data` JSON)
BEGIN



	DECLARE v_ref_id VARCHAR(255);

	DECLARE v_store_id INT(20);

    DECLARE v_stock INT DEFAULT NULL; 

    DECLARE v_is_active INT DEFAULT NULL; 

    DECLARE v_price decimal(10,2) DEFAULT NULL;  

	DECLARE v_cost_price decimal(10,2) DEFAULT NOT NULL;

    DECLARE v_mrp decimal(10,2) DEFAULT NULL;





   

	SET v_ref_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.junq_id'));

	SET v_store_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.store_id'));

    SET v_stock = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.stock')),0);

	SET v_is_active = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.is_active')),1);



    SET v_price	        = CAST(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.price'))as DECIMAL(10,2));

    SET v_cost_price	= CAST(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.cost_price'))as DECIMAL(10,2));

    SET v_mrp	        = CAST(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.mrp'))as DECIMAL(10,2));

    



	START TRANSACTION;

		 

-- 		insert into storewise_variants (ref_id,store_id,stock,is_active) values(v_junq_id,v_store_id,v_quantity,v_is_active);

-- 		SET v_category_id	= LAST_INSERT_ID();

	

	if (select COUNT(*) from storewise_variants where store_id = v_store_id and ref_id = v_ref_id) != 0 THEN 

     update storewise_variants

     set stock = v_stock,

         price = v_price,

	     cost_price   = v_cost_price,

	     mrp          = v_mrp,

	     is_active    = v_is_active,

         updated_at = NOW() 

     where store_id = v_store_id and ref_id = v_ref_id;

--     SELECT 1 AS flag, 'Inserted Successfully' AS msg,LAST_INSERT_ID() as storewise_variants_id;

    ELSE

     insert into storewise_variants (ref_id,store_id,price,cost_price,mrp,stock,is_active) values(v_ref_id,v_store_id,v_price,v_cost_price,v_mrp,v_stock,v_is_active);

-- 	SELECT 1 AS flag, 'Inserted Successfully' AS msg,LAST_INSERT_ID() as storewise_variants_id;

END IF;

	

		

        

	COMMIT;



END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spUpdateAddress` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spUpdateAddress`(IN `p_data` JSON)
BEGIN



	DECLARE v_address_id INT(11) DEFAULT NOT NULL;

    DECLARE v_user_id INT(11) DEFAULT NOT NULL;

	DECLARE v_address1 varchar(70);

    DECLARE v_address2 varchar(70);
    DECLARE v_landmark TEXT;

    DECLARE v_city varchar(255) DEFAULT NULL;

    DECLARE v_pincode INT(255) DEFAULT NULL;

    DECLARE v_state varchar(255) DEFAULT NULL;

    DECLARE v_name varchar(50) DEFAULT NULL;

    DECLARE v_address_type varchar(50) DEFAULT NULL;

    DECLARE v_is_default int(5) DEFAULT 0;

	DECLARE v_phone varchar(50) DEFAULT NULL;

    DECLARE v_longitude decimal(11,8) DEFAULT NULL;

    DECLARE v_latitude decimal(12,10) DEFAULT NULL;



    SET v_address_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.address_id'));

    SET v_user_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.user_id'));

	SET v_address1 = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.address1'));

	SET v_address2 = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.address2'));
	SET v_landmark   = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.landmark'));

	SET v_city = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.city'));

	SET v_pincode = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.pincode'));

	SET v_state = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.state'));

    SET v_name = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.name'));

    SET v_address_type = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.address_type'));

    SET v_is_default = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.is_default')),0); 

    SET v_phone = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.phone'));

    SET v_longitude = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.longitude'));

    SET v_latitude = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.latitude'));



	START TRANSACTION;

    

		UPDATE

			user_address ua

		SET ua.address1 = v_address1,

		    ua.address2 = v_address2,
		    ua.landmark = v_landmark,

			ua.city = v_city,

			ua.state = v_state,

			ua.pincode = v_pincode,

			ua.name = v_name,

			ua.address_type = v_address_type,

			ua.is_default = v_is_default,

			ua.phone = v_phone,

			ua.longitude = v_longitude,

			ua.latitude = v_latitude,

			ua.updated_at  = NOW()

		WHERE

			ua.id = v_address_id;

		

      if v_is_default = 1 then

	  update user_address ua

	  set ua.is_default = 0 

	  where ua.user_id = v_user_id and ua.id != v_address_id;

	 end if;

        

        SELECT 1 AS flag, 'Updated Successfully' AS msg;

	COMMIT;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spUpdateBanner` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spUpdateBanner`(IN `p_data` JSON)
BEGIN

	DECLARE v_name VARCHAR(255) DEFAULT NULL;

	DECLARE v_description VARCHAR(255) DEFAULT NULL;

    DECLARE v_type VARCHAR(255) DEFAULT NOT NULL;

    DECLARE v_url VARCHAR(300) DEFAULT NULL;

    DECLARE v_product_id int DEFAULT NULL;

    DECLARE v_variant_id int DEFAULT NULL;

    DECLARE v_category_id int DEFAULT NULL;

    DECLARE v_sub_category_id int DEFAULT NULL;

    DECLARE v_meta VARCHAR(255) DEFAULT NULL;

    DECLARE v_valid_from datetime DEFAULT NULL;

    DECLARE v_valid_till datetime DEFAULT NULL;

    DECLARE v_is_active int DEFAULT 1;

    DECLARE v_banner_id INT(11) DEFAULT NOT NULL;

          

    SET v_name 		    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.name'));

    SET v_description 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.description'));

    SET v_type       	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.type'));

    SET v_url      	    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.url'));

    SET v_product_id    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.product_id'));

    SET v_variant_id    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.variant_id'));

    SET v_category_id   = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.category_id'));

    SET v_sub_category_id   = if(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sub_category_id')) = 'null',NULL,JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sub_category_id')));

    SET v_meta          = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.meta'));

    SET v_valid_from    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.valid_from'));

    SET v_valid_till    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.valid_till'));

    SET v_is_active     = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.is_active')),1);

    SET v_banner_id     = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.banner_id'));



	START TRANSACTION;

    

		UPDATE

			banners o

		    SET 

		    o.name = v_name,

		    o.description = if(v_description = 'null',NULL,v_description),

		    o.`type`= v_type,

		    o.url = v_url,

		    o.valid_from = v_valid_from,

		    o.valid_till = v_valid_till,

		    o.product_id = v_product_id,

		    o.variant_id = v_variant_id,

		    o.sub_category_id = v_sub_category_id,

		    o.category_id = v_category_id,

		    o.meta = v_meta,

		    o.is_active = v_is_active,

			o.updated_at  = NOW()

		WHERE

			o.id = v_banner_id;

        

        SELECT 1 as status, 'Update record successfully' as msg;



	COMMIT;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spUpdateCategory` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spUpdateCategory`(IN `p_data` JSON)
BEGIN



	DECLARE v_category_id INT DEFAULT NULL; 

	DECLARE v_name VARCHAR(255) DEFAULT NULL; 

	DECLARE v_description VARCHAR(255) DEFAULT NULL; 



    SET v_category_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.category_id'));

	SET v_name = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.name'));

	SET v_description = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.description'));



	START TRANSACTION;

    

		UPDATE

			category c

		SET c.name = v_name, 

			c.description = if(v_description = 'null',NULL,v_description),

			c.updated_at  = NOW()

		WHERE

			c.id = v_category_id;

        

        SELECT true as status, 'Update record successfully' as msg;



	COMMIT;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spUpdateOffer` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spUpdateOffer`(IN `p_data` JSON)
BEGIN



	DECLARE v_offer_id INT DEFAULT NOT NULL;

    DECLARE v_name VARCHAR(255) DEFAULT NOT NULL;

	DECLARE v_description LONGTEXT DEFAULT NULL;

	DECLARE v_coupon_code VARCHAR(50) DEFAULT NULL;

	DECLARE v_valid_from datetime DEFAULT NULL;

    DECLARE v_valid_till datetime DEFAULT NULL;

    DECLARE v_is_percent int DEFAULT 0;

    DECLARE v_percent_discount double DEFAULT NULL;

    DECLARE v_amount_discount double DEFAULT NULL;

    DECLARE v_product_id int DEFAULT NULL;

    DECLARE v_variant_id int DEFAULT NULL;

    DECLARE v_category_id int DEFAULT NULL;

    DECLARE v_upto decimal(10,2) DEFAULT NULL;

    DECLARE v_min_amount decimal(10,2) DEFAULT NULL;

    DECLARE v_tag_id int DEFAULT NULL;

   

    SET v_offer_id 		    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.offer_id'));    

    SET v_name 		    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.name'));

    SET v_description 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.description'));

    SET v_coupon_code 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.coupon_code'));

    SET v_valid_from    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.valid_from'));

    SET v_valid_till    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.valid_till'));

    SET v_is_percent    = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.is_percent')),0);

    SET v_percent_discount = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.percent_discount'));

    SET v_amount_discount  = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.amount_discount'));

    SET v_product_id    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.product_id'));

    SET v_variant_id    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.variant_id'));

    SET v_category_id   = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.category_id'));

    SET v_upto          = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.upto'));

    SET v_min_amount          = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.min_amount'));

    SET v_tag_id        = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.tag_id'));



	START TRANSACTION;

    

		UPDATE

			offers o

		    SET 

		    o.name = v_name,

		    o.description = if(v_description = 'null',NULL,v_description),

		    o.coupon_code = v_coupon_code,

		    o.valid_from = v_valid_from,

		    o.valid_till = v_valid_till,

		    o.is_percent = v_is_percent,

		    o.percent_discount = v_percent_discount,

		    o.amount_discount = v_amount_discount,

		    o.product_id = v_product_id,

		    o.variant_id = v_variant_id,

		    o.category_id = v_category_id,

		    o.upto = v_upto,

		    o.min_amount = v_min_amount,

		    o.tag_id = v_tag_id,

			o.updated_at  = NOW()

		WHERE

			o.id = v_offer_id;

        

        SELECT 1 as status, 'Update record successfully' as msg;



	COMMIT;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spUpdateOrder` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spUpdateOrder`(IN `p_data` JSON)
BEGIN

	

	DECLARE v_order_id INT(11) DEFAULT NULL;

    DECLARE v_offer_id int DEFAULT NOT NULL;

    DECLARE v_user_id int DEFAULT NOT NULL;

    DECLARE v_store_id int DEFAULT NOT NULL;

    DECLARE v_brand_id int DEFAULT NOT NULL;

	DECLARE v_address_id int(11) DEFAULT NULL;

	DECLARE v_status VARCHAR(255) DEFAULT NULL;

    DECLARE v_sub_total DECIMAL(10,2) DEFAULT NOT NULL;

	DECLARE v_gst DECIMAL(10,2) DEFAULT NOT NULL;

	DECLARE v_discount DECIMAL(10,2) DEFAULT NOT NULL;

    DECLARE v_delivery_cost DECIMAL(10,2) DEFAULT NOT NULL;

	

	DECLARE v_grand_total DECIMAL(10,2) DEFAULT NOT NULL;

    DECLARE v_orderDetails JSON;

    DECLARE v_updated_by INT(11) DEFAULT NULL;

    DECLARE v_reason VARCHAR(128) DEFAULT NULL;

  

   DECLARE v_meta JSON DEFAULT NULL;

   DECLARE v_order_type VARCHAR(128) DEFAULT NULL;

   DECLARE v_last_payment_id INT(11) DEFAULT NULL;

  

   

    DECLARE i  INT DEFAULT 0;

	DECLARE v_product_id int DEFAULT NOT NULL;

    DECLARE v_variant_id int DEFAULT NOT NULL;   	

	DECLARE v_quantity int DEFAULT NOT NULL;

    DECLARE v_buying_price DECIMAL(10,2) DEFAULT NOT NULL;



	SET v_order_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.order_id'));

    SET v_offer_id 		= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.offer_id'));

    SET v_user_id 		= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.user_id'));

    SET v_store_id 		= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.store_id'));

    SET v_brand_id 		= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.brand_id'));

    SET v_address_id = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.address_id'));

    SET v_status = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.status'));

   

    SET v_sub_total		= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sub_total'));

    SET v_gst 			= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.gst'));

    SET v_discount		= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.discount'));

    SET v_delivery_cost		= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.delivery_cost'));

    

    SET v_grand_total 	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.grand_total'));

   

    SET v_orderDetails  = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.orderDetails'));

   

    SET v_updated_by    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.updated_by'));

    SET v_reason        = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.reason'));

--     SET v_meta          = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.meta'));

    SET v_order_type    = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.order_type'));





  

	START TRANSACTION;

    

		UPDATE orders o

		SET o.sub_total = v_sub_total,

		    o.gst = v_gst,

		    o.discount = v_discount,

		    o.delivery_cost = v_delivery_cost,

		    o.grand_total = v_grand_total,

		    o.status  = v_status,

		    o.address_id    =v_address_id,

		    o.offer_id  = v_offer_id,

		    o.store_id = v_store_id,

		    o.order_type = v_order_type,

			o.updated_at  	= NOW()

		WHERE

			o.id 			= v_order_id;

		

	 DELETE from order_details where order_id = v_order_id;

	  

     WHILE i < JSON_LENGTH(v_orderDetails) DO

     

     SET v_product_id = JSON_EXTRACT(v_orderDetails,CONCAT('$[',i,'].product_id'));

     SET v_variant_id = JSON_EXTRACT(v_orderDetails,CONCAT('$[',i,'].variant_id'));      

     SET v_quantity = JSON_EXTRACT(v_orderDetails,CONCAT('$[',i,'].quantity'));

     SET v_buying_price = JSON_EXTRACT(v_orderDetails,CONCAT('$[',i,'].buying_price'));

     

	 INSERT INTO order_details( order_id, product_id, variant_id, quantity, buying_price ) values( v_order_id, v_product_id, v_variant_id, v_quantity, v_buying_price);

     

	 SELECT i + 1 INTO i;

	

     END WHILE;



         insert into order_log(order_id,status,updated_by,reason) 

         values(v_order_id,v_status,v_updated_by,v_reason);

     

        

    INSERT INTO delivery(order_id, status, delivery_cost)

	values(v_order_id, 'ACCEPTED', ifnull(v_delivery_cost,0));

		

        

   if v_order_type = 'COD'

     then

--         update orders o set o.meta  = v_meta where o.id =  v_order_id;

       

--        INSERT INTO orders(user_id, status, address_id,offer_id, brand_id, sub_total, gst, discount, grand_total,store_id,order_type) 

-- 	   values(v_updated_by,'order_unpaid', v_address_id, v_offer_id,v_brand_id, v_sub_total, v_gst, v_discount, v_grand_total,v_store_id,v_order_type);

        UPDATE orders o

		SET o.status  = 'order_unpaid',

			o.updated_at  	= NOW()

		WHERE

			o.id 			= v_order_id;

       

        insert into order_log(order_id,status,updated_by,reason) 

        values(v_order_id,'order_unpaid',v_updated_by,v_reason);

        

        INSERT INTO `payment`(`user_id`,payment_type ,`order_id`,`amount`,`status`)

	    VALUES ( v_updated_by,'COD', v_order_id, v_grand_total, 'created');

	   

	

        SET v_last_payment_id	= LAST_INSERT_ID();	

--         update payment p set p.razorpay_ref_id = CONCAT('JF_','pay_',v_last_payment_id) where p.id = v_last_payment_id;

       

        update orders o set o.payment_id  = v_last_payment_id where o.id =  v_order_id;

    

        INSERT INTO `payment_log`(`payment_id`,`status`)

	    VALUES (v_last_payment_id, 'created');

	   

	   UPDATE product_variant pv,

         (select o.id,od.variant_id as variant_id  ,o.status,pv.stock, od.quantity,(pv.stock - od.quantity) as remaining_stock from orders o 

          left join order_details od on od.order_id =o.id

          left join product_variant pv on pv.id = od.variant_id 

          where o.status ="order_paid" and o.id = v_order_id

         ) src 

        SET pv.stock = src.remaining_stock

        WHERE pv.id = src.variant_id;

   end if;

  

  UPDATE storewise_variants pv,

         (select o.id,od.variant_id as variant_id ,pv.stock,pv.ref_id, od.quantity,(pv.stock - od.quantity) as remaining_stock from orders o 

          left join order_details od on od.order_id =o.id

          left join product_variant pv on pv.id = od.variant_id 

          where o.status ="order_paid" and o.id = v_order_id

         ) src 

        SET pv.stock = src.remaining_stock,

            pv.updated_at = NOW()

        WHERE pv.store_id = v_store_id and pv.ref_id = src.ref_id; 

       

       

        

		

        SELECT 1 as status, 'Updated order successfully' as msg;

       



	COMMIT;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spUpdateProduct` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spUpdateProduct`(IN p_data JSON)
BEGIN



	DECLARE v_product_id INT(11) DEFAULT NULL;

	DECLARE v_name VARCHAR(100) DEFAULT NULL;

	DECLARE v_description VARCHAR(500) DEFAULT NULL;

	DECLARE v_sku VARCHAR(100) DEFAULT NULL;

	DECLARE v_category_id INT(11) DEFAULT NULL;

    DECLARE v_sub_category_id INT(11) DEFAULT NULL;

    DECLARE v_sub_category_group_id INT(11) DEFAULT NULL;

	DECLARE v_status VARCHAR(100) DEFAULT NULL;

	DECLARE v_is_active INT(11) DEFAULT 1;

    DECLARE v_meta JSON;





	SET v_product_id 	= JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.product_id'));

    SET v_name 			= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.name'));

    SET v_description	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.description'));

    SET v_sku 			= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sku'));

    SET v_category_id	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.category_id'));

    SET v_sub_category_id	=JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sub_category_id'));

    SET v_sub_category_group_id	=JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.sub_category_group_id'));

    SET v_status 		= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.status'));

    SET v_is_active     = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.is_active')),1);

    SET v_meta 			= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.meta'));



	START TRANSACTION;

    

		UPDATE

			products p

		SET p.name    		= v_name,

			p.description 	= if(v_description = 'null',NULL,v_description),

			p.SKU 			= v_sku,

			p.category_id   = v_category_id,

			p.sub_category_id = v_sub_category_id,

			p.sub_category_group_id = v_sub_category_group_id,

			p.status 		= v_status,

			p.is_active     = v_is_active,

			p.meta 			= v_meta,

			p.updated_at  	= NOW()

		WHERE

			p.id 			= v_product_id;

     

       

			UPDATE

			product_variant pv

		SET pv.is_active    = v_is_active,

			pv.updated_at  	= NOW()

		WHERE

			pv.product_id = v_product_id;

		

		 SELECT 1 as status, 'Updated Product successfully' as msg;



	COMMIT;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spUpdateReview` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spUpdateReview`(IN `p_data` JSON)
BEGIN



	DECLARE v_review_id INT DEFAULT NOT NULL;

	DECLARE v_rating int DEFAULT NOT NULL;

	DECLARE v_description VARCHAR(255) DEFAULT NULL;

   

    SET v_review_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.review_id'));

    SET v_rating = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.rating'));

    SET v_description = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.description'));



	START TRANSACTION;

    

		UPDATE

			review r

		    SET r.rating    = v_rating,

		    r.description = if(v_description = 'null',NULL,v_description),

			r.updated_at  = NOW()

		WHERE

			r.id = v_review_id;

        

        SELECT 1 as status, 'Update record successfully' as msg;



	COMMIT;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spUpdateStockConfig` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spUpdateStockConfig`(IN `p_data` JSON)
BEGIN

	

	DECLARE v_out_of_stock int(11) default null;

	DECLARE v_finishing_soon int(11) default null;

	DECLARE v_in_stock int(11) default null;

	DECLARE v_plenty int(11) default null;

-- 	DECLARE v_new_in_stock int(11) default null;

    DECLARE v_delivery_fee decimal(10,2) default null;

    DECLARE v_delivery_fee_threshold decimal(10,2) default null;

    DECLARE v_min_order_value decimal(10,2) default null;



	SET v_out_of_stock = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.out_of_stock')),0);

	SET v_finishing_soon = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.finishing_soon')),1);

	SET v_in_stock = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.in_stock')),10);

	SET v_plenty = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.plenty')),100);

    SET v_delivery_fee = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.delivery_fee')),0);

    SET v_delivery_fee_threshold = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.delivery_fee_threshold')),0);

    SET v_min_order_value = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.min_order_value')),0);

-- 	SET v_new_in_stock = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.new_in_stock')),7);

    

	START TRANSACTION;

	   

		UPDATE brand_stock_config bsc

		SET bsc.out_of_stock = v_out_of_stock,

		    bsc.finishing_soon = v_finishing_soon,

		    bsc.in_stock = v_in_stock,

		    bsc.plenty = v_plenty,

-- 		    bsc.new_in_stock = v_new_in_stock,

		    bsc.delivery_fee = v_delivery_fee,

		    bsc.delivery_fee_threshold = v_delivery_fee_threshold,

		    bsc.min_order_value = v_min_order_value,

			bsc.updated_at  	= NOW();

		SELECT 1 AS flag, 'Updated Successfully' AS msg;

        

	COMMIT;



END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spUpdateSubCategory` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spUpdateSubCategory`(IN `p_data` JSON)
BEGIN



	DECLARE v_sub_category_id INT DEFAULT NULL; 

	DECLARE v_name VARCHAR(255) DEFAULT NULL; 

	DECLARE v_description VARCHAR(255) DEFAULT NULL; 



    SET v_sub_category_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.sub_category_id'));

	SET v_name = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.name'));

	SET v_description = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.description'));



	START TRANSACTION;

    

		UPDATE

			sub_category sc

		SET sc.name = v_name, 

			sc.description = if(v_description = 'null',NULL,v_description),

			sc.updated_at  = NOW()

		WHERE

			sc.id = v_sub_category_id;

        

        SELECT true as status, 'Update record successfully' as msg;



	COMMIT;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spUpdateSubCategoryGroup` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spUpdateSubCategoryGroup`(IN `p_data` JSON)
BEGIN



	DECLARE v_sub_category_group_id INT DEFAULT NULL; 

	DECLARE v_name VARCHAR(255) DEFAULT NULL; 

	DECLARE v_description VARCHAR(255) DEFAULT NULL; 

    DECLARE v_type VARCHAR(255) DEFAULT NULL; 



    SET v_type = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.type'));

    SET v_sub_category_group_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.sub_category_group_id'));

	SET v_name = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.name'));

	SET v_description = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.description'));



	START TRANSACTION;

    

      CASE v_type

	

		WHEN 'inventory' THEN

		

		UPDATE

			sub_category_group sc

		SET sc.name = v_name, 

			sc.updated_at  = NOW()

		WHERE

			sc.id = v_sub_category_group_id;

		

		ELSE

        UPDATE

			sub_category_group sc

		SET sc.name = v_name, 

	      	sc.description = if(v_description = 'null',NULL,v_description),

			sc.updated_at  = NOW()

		WHERE

			sc.id = v_sub_category_group_id;

		

	END CASE;

		

        SELECT true as status, 'Update record successfully' as msg;



	COMMIT;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spUpdateTag` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spUpdateTag`(IN `p_data` JSON)
BEGIN



	DECLARE v_tag_id INT(11) DEFAULT NOT NULL;

    DECLARE v_description varchar(255) DEFAULT NULL;

    DECLARE v_name varchar(50) DEFAULT NULL;



    SET v_tag_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.tag_id'));

	SET v_description = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.description'));

    SET v_name = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.name'));

   

	START TRANSACTION;

    

		UPDATE

			tags t

		SET t.name = v_name,

		    t.description = if(v_description = 'null',NULL,v_description),

			t.updated_at  = NOW()

		WHERE

			t.id = v_tag_id;

        

        SELECT 1 AS flag, 'Updated Successfully' AS msg;

	COMMIT;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spUpdateVariant` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spUpdateVariant`(IN p_data JSON)
BEGIN

	

	DECLARE v_variant_id INT(11) DEFAULT NULL;

    DECLARE v_product_id INT(11) DEFAULT NULL;

    DECLARE v_variant_name VARCHAR(100) DEFAULT NOT NULL;

	DECLARE v_variant_sku VARCHAR(100) DEFAULT NOT NULL; 

    DECLARE v_price decimal(10,2) DEFAULT NULL;  

	DECLARE v_cost_price decimal(10,2) DEFAULT NOT NULL;

    DECLARE v_mrp decimal(10,2) DEFAULT NULL;

	DECLARE v_weight decimal(10,2) DEFAULT NULL;

	DECLARE v_stock INT(11) DEFAULT 0;

    DECLARE v_is_active INT(11) DEFAULT 1;

    DECLARE v_store_id INT(11) DEFAULT NULL;

    DECLARE v_store_is_active INT(11) DEFAULT NULL;

    DECLARE v_ref_id VARCHAR(20) DEFAULT NULL;

   

   

    DECLARE v_check int(5) DEFAULT 0;

   

    SET v_variant_id    = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.variant_id'));

	SET v_variant_name 			= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.variant_name'));

--  SET v_description	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.description'));

    SET v_variant_sku 			= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.variant_sku'));

    SET v_price	        = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.price'));

    SET v_cost_price	= JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.cost_price'));

    SET v_mrp	        = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.mrp'));

    SET v_is_active 	= ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.variant_is_active')),1);

    SET v_weight        = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.weight'));

    SET v_stock         = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.variant_stock')),0);

    SET v_store_id      = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.store_id'));

    SET v_store_is_active      = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.store_is_active')),1);

--     SET v_ref_id     = JSON_UNQUOTE(JSON_EXTRACT(p_data,'$.ref_id'));

    

   

	START TRANSACTION;

      

    UPDATE

			product_variant pv

		SET pv.name    		= v_variant_name,

			pv.sku 			= v_variant_sku,

			pv.is_active    = v_is_active,

			pv.weight       = v_weight,

			pv.updated_at  	= NOW()

		WHERE

			pv.id 			= v_variant_id;

		

		

		SET v_product_id = (select pv.product_id from product_variant pv where pv.id = v_variant_id);

	

	    SET v_check = (select count(*) from product_variant pv where pv.product_id = v_product_id and pv.is_active = 1);

	   

	 IF(v_check = 0 )

	   THEN

	   update products p

	   set p.is_active    = 0,

	   	   p.updated_at   = NOW()

		WHERE

			p.id 		  = v_product_id;

		

		 UPDATE storewise_variants o ,

         (select o.ref_id from product_variant o 

          where o.product_id = v_product_id

         ) src 

        set o.is_active    = 0,

            o.updated_at   = NOW() 

        WHERE o.ref_id = src.ref_id;

       

	 END IF; 



	if v_store_id IS NOT NULL

	THEN

	

	 SET v_ref_id = (select pv.ref_id from storewise_variants sv

                     inner join product_variant pv on pv.ref_id = sv.ref_id 

                     where sv.store_id = v_store_id and pv.id = v_variant_id);

	

    if v_ref_id IS NOT NULL

	 THEN

        UPDATE storewise_variants o ,

         (select o.ref_id from product_variant o 

          where o.id = v_variant_id

         ) src 

        set o.stock = v_stock,

            o.price        = v_price,

			o.cost_price   = v_cost_price,

			o.mrp          = v_mrp,

            o.is_active    = if(v_is_active = 0,0,v_store_is_active),

         o.updated_at = NOW() 

        WHERE o.store_id = v_store_id and o.ref_id = src.ref_id; 

     ELSE

        insert into storewise_variants (ref_id,store_id,price,cost_price,mrp,stock,is_active) values((select ref_id from product_variant where id = v_variant_id ),v_store_id,v_price,v_cost_price,v_mrp,v_stock,1);

     END IF;

  END IF;

	      

     

        SELECT 1 as status, 'Updated product variant successfully' as msg;

       



	COMMIT;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spUploadImage` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spUploadImage`(IN `p_data` JSON)
BEGIN

	DECLARE v_product_id INT(11) DEFAULT NOT NULL;

    DECLARE v_variant_id INT(11) DEFAULT NOT NULL;

    DECLARE v_category_id INT(11) DEFAULT NOT NULL;

    DECLARE v_sub_category_id INT(11) DEFAULT NOT NULL;

    DECLARE v_sub_category_group_id INT(11) DEFAULT NOT NULL;

    DECLARE v_offer_id INT(11) DEFAULT NOT NULL;

    DECLARE v_banner_id INT(11) DEFAULT NOT NULL;

    DECLARE v_user_id INT(11) DEFAULT NOT NULL;

    DECLARE v_image varchar(500) DEFAULT NULL;

    DECLARE v_image_id int(11) default null;

   

    SET v_product_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.product_id'));

    SET v_variant_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.variant_id'));

    SET v_category_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.category_id'));

    SET v_sub_category_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.sub_category_id'));

    SET v_sub_category_group_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.sub_category_group_id'));

    SET v_offer_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.offer_id'));

    SET v_banner_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.banner_id'));

    SET v_user_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.user_id'));

	SET v_image = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.location'));

	  

	  IF (v_product_id IS NOT NULL) 

	      THEN

            insert into product_images (product_id,variant_id,images) 

	        values(v_product_id,v_variant_id,v_image);

	        SET v_image_id	= LAST_INSERT_ID();

     ELSEIF (v_category_id IS NOT NULL)

          THEN

            INSERT  into category_images (category_id,images) 

	        values(v_category_id,v_image);

	        SET v_image_id	= LAST_INSERT_ID();

	 ELSEIF (v_offer_id IS NOT NULL)

          THEN

            INSERT  into offer_image (offer_id,original_url) 

	        values(v_offer_id,v_image);

	        SET v_image_id	= LAST_INSERT_ID();

	ELSEIF (v_sub_category_id IS NOT NULL)

          THEN

            INSERT  into subcategory_images (sub_category_id,images) 

	        values(v_sub_category_id,v_image);

	        SET v_image_id	= LAST_INSERT_ID();

	ELSEIF (v_sub_category_group_id IS NOT NULL)

          THEN

            INSERT  into sub_category_group_images (sub_category_group_id,images) 

	        values(v_sub_category_group_id,v_image);

	        SET v_image_id	= LAST_INSERT_ID();

	ELSEIF (v_banner_id IS NOT NULL)

          THEN

            INSERT  into banner_images (banner_id,images) 

	        values(v_banner_id,v_image);

	        SET v_image_id	= LAST_INSERT_ID();

     ELSE

            update users u

	    	set u.user_profile_image = v_image

	    	where u.id = v_user_id;

	        SET v_image_id	= v_user_id;

    END IF;



  

		SELECT 1 AS flag, 'Uploaded Successfully' AS msg,v_image as url,v_image_id as image_id;

       

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spuploadInventory` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spuploadInventory`(in p_data JSON)
BEGIN



    DECLARE v_url varchar(500) DEFAULT NULL;



   DECLARE v_inventory_id int(11) DEFAULT NULL;



	SET v_url = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.location'));

	

            insert into inventory (url) 

	        values(v_url);

	       

	        SET v_inventory_id	= LAST_INSERT_ID();





  

		SELECT 1 AS flag, 'Uploaded Successfully' AS msg,v_url as url,v_inventory_id as inventory_id;

       

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spuploadStore` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spuploadStore`(IN `p_data` JSON)
BEGIN



    DECLARE v_url varchar(500) DEFAULT NULL;



   DECLARE v_store_inventory_id int(11) DEFAULT NULL;



	SET v_url = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.location'));

	

            insert into store_inventory (url) 

	        values(v_url);

	       

	        SET v_store_inventory_id	= LAST_INSERT_ID();





  

		SELECT 1 AS flag, 'Uploaded Successfully' AS msg,v_url as url,v_store_inventory_id as store_inventory_id;

       

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spUserCouponOffer` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spUserCouponOffer`(in p_data JSON)
BEGIN

    DECLARE v_user_id int DEFAULT null;

    SET v_user_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.user_id'));

	START TRANSACTION;
    -- Early bird offer
    select oo.* from user_offer uf
		join offers oo on uf.offer_id = oo.id
		where uf.user_id = v_user_id and oo.tag_id =1;

	select * from offers
	where valid_till > current_timestamp() and tag_id != '1' or tag_id is null;
    COMMIT;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spValidateCoupon` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spValidateCoupon`(IN p_data JSON)
BEGIN
	DECLARE v_code VARCHAR(100) default null;
	DECLARE v_total int default 0;
	DECLARE v_check int default 0;
    DECLARE v_variant int default null;
	DECLARE v_category int default null;
    DECLARE v_is_percent int default 1;
    DECLARE v_min_amount int default null;
	

	SET v_code		= JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.coupon_code'));
	SET v_total		= JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.sub_total'));

	
	START TRANSACTION;
		SET v_check = (SELECT count(*) FROM offers WHERE coupon_code = v_code AND valid_till > CURRENT_TIMESTAMP() LIMIT 1);
	    SET v_variant = (SELECT variant_id FROM offers WHERE coupon_code = v_code AND valid_till > CURRENT_TIMESTAMP() LIMIT 1);
	    SET v_category = (SELECT category_id FROM offers WHERE coupon_code = v_code AND valid_till > CURRENT_TIMESTAMP() LIMIT 1);
        SET v_is_percent = (SELECT count(*) FROM offers WHERE coupon_code = v_code AND valid_till > CURRENT_TIMESTAMP() and is_percent=1 LIMIT 1);
-- 		if (v_check = 1 and v_is_percent = 1) 
--          then
-- 			select oo.name, oo.coupon_code,
--             CAST((v_total - (v_total * oo.percent_discount/100)) as DECIMAL(10,2)) as 'sub_total',
--             CAST((v_total * oo.percent_discount/100) as DECIMAL(10,2)) as 'discount'
--             from offers oo WHERE coupon_code = v_code LIMIT 1;
--             # 50-(50*15/100) = 42.50
--         elseif (v_check = 1 and v_is_percent = 0)
-- 			then 
--             select oo.name, oo.coupon_code,
--             CAST((v_total - oo.amount_discount) as DECIMAL(10,2)) as 'sub_total',
-- 			CAST((v_total - (v_total - oo.amount_discount)) as DECIMAL(10,2)) as 'discount'
--             from offers oo WHERE coupon_code = v_code LIMIT 1;
-- 		else 
--             select 'invalid offer' as offer_name, 0 as discount;
--         end if;
       
  if (v_check = 1) 
    then
         
         
      if(v_variant IS NOT NULL)
       then
			select oo.id as offer_id, oo.name, oo.coupon_code,pv.id as variant_id,pv.product_id,1 as quantity,oo.min_amount,pv.price,pv.mrp,"variant" as offer_type,
			pv.weight,p.name as product_name,pv.name as variant_name,
			 CAST(0 as DECIMAL(10,2)) as sub_total,
             CAST(0 as DECIMAL(10,2)) as discount
            from offers oo 
            left join product_variant pv on pv.id = v_variant
            left join products p on p.id = pv.product_id
           WHERE coupon_code = v_code and oo.variant_id = v_variant  LIMIT 1;
            # 50-(50*15/100) = 42.50
         
      elseif(v_category IS NOT NULL)
        then
			 select oo.id as offer_id, oo.name, oo.coupon_code,oo.upto,oo.min_amount,v_category as category_id,1 as quantity,"category" as offer_type,
-- 			 CAST((v_total - (v_total * oo.percent_discount/100)) as DECIMAL(10,2)) as 'sub_total',
             if(CAST((v_total * oo.percent_discount/100) as DECIMAL(10,2)) > oo.upto,oo.upto,CAST((v_total * oo.percent_discount/100) as DECIMAL(10,2)) ) as discount,
              CAST((v_total - if(CAST((v_total * oo.percent_discount/100) as DECIMAL(10,2)) > oo.upto,oo.upto,CAST((v_total * oo.percent_discount/100) as DECIMAL(10,2)) )) as DECIMAL(10,2)) as sub_total
             from offers oo
             WHERE coupon_code = v_code  LIMIT 1;
            
      else
          if (v_check = 1 and v_is_percent = 1) 
            then
			select oo.id as offer_id, oo.name, oo.coupon_code,pv.id as variant_id,pv.product_id,oo.min_amount,0 as quantity,"percent" as offer_type,ifnull(pv.price,"0.00") as price,ifnull(pv.mrp,"0.00") as mrp,
             CAST((v_total - (v_total * oo.percent_discount/100)) as DECIMAL(10,2)) as 'sub_total',
             CAST((v_total * oo.percent_discount/100) as DECIMAL(10,2)) as 'discount'
             from offers oo 
              left join product_variant pv on pv.id = v_variant
            WHERE coupon_code = v_code LIMIT 1;
             # 50-(50*15/100) = 42.50
          elseif (v_check = 1 and v_is_percent = 0)
			then 
            select oo.id as offer_id, oo.name, oo.coupon_code,pv.id as variant_id,pv.product_id,oo.min_amount,0 as quantity,"amount" as offer_type,ifnull(pv.price,"0.00") as price,ifnull(pv.mrp,"0.00") as mrp,
            CAST((v_total - oo.amount_discount) as DECIMAL(10,2)) as 'sub_total',
			CAST((v_total - (v_total - oo.amount_discount)) as DECIMAL(10,2)) as 'discount'
            from offers oo
             left join product_variant pv on pv.id = v_variant
           WHERE coupon_code = v_code LIMIT 1;
           end if;
          
          
          
       end if;
          
		else 
            select 'invalid offer' as offer_name, 0 as discount;
        end if;
    COMMIT;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `spVerifyOtp` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`JunqFoodsAdmin`@`%` PROCEDURE `spVerifyOtp`(IN p_data JSON)
BEGIN

DECLARE v_temp_id INT(11) DEFAULT NOT NULL;

	DECLARE v_user_id INT(11) DEFAULT NOT NULL;

    DECLARE v_customer_id INT(11) DEFAULT NOT NULL;

    DECLARE v_expert_id INT(11) DEFAULT NOT NULL;

    DECLARE v_brand_id INT(11) DEFAULT NOT NULL;

    DECLARE v_centre_id INT(11) DEFAULT NOT NULL;

    DECLARE v_role_id INT(11) DEFAULT NOT NULL;

    DECLARE v_admin_type_id INT(11) DEFAULT NOT NULL;

    DECLARE v_first_name varchar(50) DEFAULT NULL;

    DECLARE v_username varchar(50) DEFAULT NULL;

    DECLARE v_email varchar(50) DEFAULT NULL;

    DECLARE v_mobile varchar(50) DEFAULT NULL;

    DECLARE v_password varchar(100) DEFAULT NULL;

    DECLARE v_category_ids JSON;

    declare v_training_type varchar(50) default null;

    DECLARE v_invited_by INT(11) DEFAULT NOT NULL;

    DECLARE v_sent_otp varchar(100) DEFAULT NOT NULL;

    DECLARE v_shortcode varchar(100) DEFAULT NOT NULL;

    DECLARE v_type varchar(10) DEFAULT NOT NULL;

    DECLARE v_invite_count int(11) DEFAULT NOT NULL;

    DECLARE v_counselling int(11) DEFAULT NOT NULL;

    DECLARE v_check tinyint(1);


   

     DECLARE i  INT DEFAULT 1;

    

    SET v_user_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.user_id'));

    SET v_role_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.role_id'));

    SET v_admin_type_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.admin_type_id'));

    SET v_first_name = ifnull(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.name')),'Edjust User');

    SET v_email = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.email'));

    SET v_mobile = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.mobile'));

    SET v_password = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.password'));

    SET v_category_ids = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.category_ids'));

    SET v_training_type = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.training_type'));

    SET v_invited_by = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.invited_by'));

    SET v_username = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.username'));

    SET v_sent_otp = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.otp'));

    SET v_shortcode = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.shortcode'));

    SET v_type = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.type'));

   

   	set v_check = (select count(t.id) from temp_user t where t.mobile = v_mobile limit 1);



     SET v_temp_id = (select t.id from temp_user t where t.mobile = v_mobile limit 1);

     SET v_user_id = (select u.id from users u where u.mobile = v_mobile limit 1); 



   

   

   

     if (v_check>0) then 

--   for new user in verify otp

  		IF v_user_id IS NULL and v_temp_id IS NOT NULL

  			THEN 

  		 	insert into users(first_name,mobile,email,sent_otp, is_newuser, otp_verified)

   			values(v_first_name,v_mobile,v_email,v_sent_otp,1,1);

  	 		SET v_user_id	= LAST_INSERT_ID();

  

   			 update temp_user t set t.otp_verified = 1, t.user_id = v_user_id where t.mobile = v_mobile;

   

   -- user already verified once

  	 		ELSEIF v_user_id IS NOT NULL 

  			THEN   

    		update temp_user t set t.otp_verified = 1, t.user_id = v_user_id,

   			 t.first_name = (select u.first_name  from users u where u.id = v_user_id)

   			 where t.mobile = v_mobile;

   			 update users t set t.otp_verified = 1 where t.id = v_user_id;

  

 		 END IF;



   

		SELECT 1 AS flag, 'Use verified Successfully!' AS msg, v_temp_id, v_user_id as user_id ;

	

	else

	

	SELECT 0 AS flag, 'user does not exist' AS msg ;

	end if;

       

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
SET @@SESSION.SQL_LOG_BIN = @MYSQLDUMP_TEMP_LOG_BIN;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2022-10-03 15:59:02
